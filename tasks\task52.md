Implement following tasks in great precission:

TASK-1:
Look at BerryBush (tscn and cs). Read them. Then copy them 1:1 and create a new object - SmallCactus. It sprite should be res://resources/pixel mood/objects/cactus2.png. Make sure you add all required things like in BerryBush. Make sure you don't break any enum numerations.
SmallCactus should drop: 1 cactus. So we need to add a new resource type: Cactus. Add it to ResourceType, ItemInformation, translations and TextureManager. It's texture in texture manager should be res://resources/solaria/resources/cactus1.png and icon res://resources/solaria/resources/icons/cactus1_icon.png.

TASK-2:
Look at Tree (tscn and cs). Read them. Then copy them 1:1 and create a new object - BigCactus. It sprite should be res://resources/pixel mood/objects/cactus5.png. Make sure you add all required things like in Tree. Make sure you don't break any enum numerations. It should drop 2 cactus and 1 wood.


TASK-3:
Look at BerryBush (tscn and cs). Read them. Then copy them 1:1 and create 2 new objects - DessertGrass and DessertDryGrass. It sprite should be accordingly: res://resources/pixel mood/objects/grass1.png and res://resources/pixel mood/objects/grass3.png. Make sure you add all required things like in BerryBush. Make sure you don't break any enum numerations. DessertGrass should drop 1 Leaf and DessertDryGrass should drop 1 Branch - that resources already exists.

TASK-4:
Furnace 2 should be able to produce everything that furnace 1 can + what furnace 2 can. furnace 3 what furnace 1,2 and 3 can. furnace 4 what furnace 1,2,3 and 4 can produce adjust - item lists in furnace 2,3,4 menus should be accordingly and their cs scripts. In tscn, copy item lists from other furnaces.