[gd_scene load_steps=5 format=3 uid="uid://bigcactus_uid"]

[ext_resource type="Script" uid="uid://bigcactus_script_uid" path="res://scenes/mapObjects/BigCactus.cs" id="1_bigcactus"]
[ext_resource type="Texture2D" uid="uid://bigcactus_texture_uid" path="res://resources/pixel mood/objects/cactus5.png" id="2_bigcactus"]
[ext_resource type="PackedScene" uid="uid://otpfc634hhga" path="res://scenes/UI/progress/ProgressBar.tscn" id="3_bb27y"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_lgydw"]
size = Vector2(6, 6)

[node name="BigCactus" type="Node2D"]
y_sort_enabled = true
script = ExtResource("1_bigcactus")

[node name="Sprite2D" type="Sprite2D" parent="."]
texture = ExtResource("2_bigcactus")

[node name="Area2D" type="Area2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="Area2D"]

[node name="StaticBody2D" type="StaticBody2D" parent="."]
position = Vector2(0, 8)

[node name="CollisionShape2D" type="CollisionShape2D" parent="StaticBody2D"]
position = Vector2(0, 11)
shape = SubResource("RectangleShape2D_lgydw")

[node name="ProgressBar" parent="." instance=ExtResource("3_bb27y")]
position = Vector2(0, 24)
scale = Vector2(1, 0.6)
