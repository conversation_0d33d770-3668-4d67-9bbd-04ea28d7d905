[gd_scene load_steps=12 format=3 uid="uid://cd5h6rv340wuq"]

[ext_resource type="Script" uid="uid://djt8qgf3s6feg" path="res://scenes/mapObjects/buildings/Windmill.cs" id="1_script"]
[ext_resource type="Texture2D" uid="uid://bc0laai1pjigu" path="res://resources/cutefantasy/windmill/windmill_base.png" id="2_windmill"]
[ext_resource type="Texture2D" uid="uid://bdrvojacws3gm" path="res://resources/cutefantasy/windmill/Windmill_Sail_Anim.png" id="3_7422c"]
[ext_resource type="PackedScene" uid="uid://otpfc634hhga" path="res://scenes/UI/progress/ProgressBar.tscn" id="3_health"]
[ext_resource type="PackedScene" uid="uid://b8xf7h2lam3pq" path="res://scenes/UI/progress/ProgressBarVertical.tscn" id="4_progress"]
[ext_resource type="PackedScene" uid="uid://besb07mcqucve" path="res://scenes/UI/buildingMenus/WindmillMenu.tscn" id="5_windmill_menu"]

[sub_resource type="Animation" id="Animation_160ic"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite2D:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(0, -32)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Sprite2D:rotation")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [0.0]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("Sprite2D/Sprite2D2:modulate")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Color(1, 1, 1, 1)]
}

[sub_resource type="Animation" id="Animation_hit"]
resource_name = "hit"
length = 0.2
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite2D:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Color(1, 1, 1, 1), Color(1, 0.42, 0.27, 1), Color(1, 1, 1, 1)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Sprite2D/Sprite2D2:modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Color(1, 1, 1, 1), Color(1, 0.419608, 0.270588, 1), Color(1, 1, 1, 1)]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_1"]
_data = {
&"RESET": SubResource("Animation_160ic"),
&"hit": SubResource("Animation_hit")
}

[sub_resource type="RectangleShape2D" id="RectangleShape2D_1"]
size = Vector2(38, 32)

[sub_resource type="RectangleShape2D" id="RectangleShape2D_2"]
size = Vector2(43, 39)

[node name="Windmill" type="Node2D"]
script = ExtResource("1_script")

[node name="Sprite2D" type="Sprite2D" parent="."]
position = Vector2(0, -32)
texture = ExtResource("2_windmill")

[node name="Sprite2D2" type="Sprite2D" parent="Sprite2D"]
texture = ExtResource("3_7422c")
hframes = 4

[node name="CraftingResourceSprite" type="Sprite2D" parent="."]
scale = Vector2(0.5, 0.5)

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_1")
}

[node name="ProgressBar" parent="." instance=ExtResource("3_health")]
position = Vector2(-2.38419e-07, 12)
scale = Vector2(2.375, 1)

[node name="ProgressBarVertical" parent="." instance=ExtResource("4_progress")]
position = Vector2(25, -13)

[node name="StaticBody2D" type="StaticBody2D" parent="."]
position = Vector2(0, -21.755)

[node name="CollisionShape2D" type="CollisionShape2D" parent="StaticBody2D"]
position = Vector2(0, 22)
shape = SubResource("RectangleShape2D_1")

[node name="PlayerDetector" type="Area2D" parent="."]
position = Vector2(0, -21.755)

[node name="CollisionShape2D" type="CollisionShape2D" parent="PlayerDetector"]
position = Vector2(0, 21)
shape = SubResource("RectangleShape2D_2")

[node name="WindmillMenu" parent="." instance=ExtResource("5_windmill_menu")]
