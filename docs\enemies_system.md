# Enemy System Architecture & Design

## System Overview

The enemy system is a comprehensive territorial AI system integrated with the existing game architecture. It provides region-based enemy spawning, territorial behavior, combat mechanics, and full save/load persistence.

## Core Architecture

### Base Classes & Interfaces
- **BaseEnemy.cs**: Abstract base class with complete AI state machine, health system, and combat integration
- **MeleeGoblin.cs**: Concrete implementation for territorial melee enemies with charging and attack mechanics
- **ICombatTarget.cs**: Interface for objects that can be targeted by enemy AI
- **EnemyType.cs**: Enum with extension methods providing enemy stats and configuration
- **EnemySaveData.cs**: Data structure for enemy persistence across save/load cycles

### Territory System
- **EnemyTerritory.cs**: Area2D-based territory management with player detection and enemy coordination
- **EnemyTerritory.tscn**: Scene template for territory areas with collision shapes and debug visualization
- Territory areas are placed in world scene under RegionSpecific nodes for organization

### Integration Points
- **Region5Manager.cs**: Complete region manager handling enemy spawning, lifecycle, and save/load
- **CommonSignals.cs**: Extended with enemy-specific signals for combat and state management
- **ResourcesManager.cs**: Extended with enemy data serialization and loading capabilities
- **PlayerController.cs**: Enhanced with TakeDamage method for enemy combat integration

## AI State Machine

### Enemy States
- **Patrolling**: Random movement within territory, idle periods, target detection
- **Pursuing**: Chase player when detected in territory or after being hit
- **Attacking**: Execute melee attacks with windup animations and area damage
- **Returning**: Return to territory center when player leaves or gets too far
- **Stunned**: Brief disable after taking damage with proper animation handling

### Behavioral Types & Territorial Behavior

#### Territorial Enemies (Defensive)
- **Territory Centers**: Fixed positions assigned to each enemy for patrolling
- **Territory Radius**: Configurable area (default 120 units) for enemy movement bounds during patrol
- **Pursuit Behavior**: When player enters territory OR hits territorial enemy, enemy can leave territory to pursue player until:
  - Player moves beyond pursuit range (200 units from enemy)
  - Enemy loses line of sight for extended period
  - Enemy dies or is stunned
- **Target Priority**: While pursuing, territorial enemies follow standard priority system (Player > Combat Buildings > Production > Walls)
- **Return Behavior**: After losing target, territorial enemies return to their designated territory center
- **Territory Constraint**: Only applies during patrol state, NOT during pursuit/combat states

#### Aggressive Enemies (Offensive)
- **No Territory Restrictions**: Can roam freely across the entire map
- **Active Hunting**: Continuously search for targets using extended detection range
- **Persistent Pursuit**: Once target acquired, pursue until target is destroyed or enemy dies
- **Target Priority System**: Same priority as territorial but with wider search radius
- **Pathfinding**: Use intelligent pathfinding to navigate around obstacles when pursuing targets
- **No Return State**: Never return to specific locations, always in active hunting mode

### Combat Mechanics
- **Melee Attacks**: 180-degree arc damage similar to player sword mechanics
- **Attack Windup**: Timed attack preparation with directional animations
- **Knockback System**: Both player and enemy knockback effects on damage
- **Health System**: HP bars with visibility management and damage feedback

### Target Priority System (Updated)
1. **Player**: Priority 100 - Always highest priority when in range
2. **Combat Buildings** (Turrets, Defenses): Priority 80 - Secondary threat
3. **Production Buildings**: Priority 60 - Economic targets
4. **Walls**: Priority 40 - Lowest building priority
5. **Distance Modifier**: Within same priority, closer targets preferred

## Animation System

### Required Animations
Each enemy requires 16 directional animations in AnimationPlayer:
- **Idle Animations**: idle_up, idle_down, idle_left, idle_right
- **Movement Animations**: move_up, move_down, move_left, move_right
- **Attack Animations**: attack_up, attack_down, attack_left, attack_right
- **Damage Animations**: got_hit_up, got_hit_down, got_hit_left, got_hit_right

### Animation Behavior
- **Automatic Direction**: System automatically switches animations based on movement/facing direction
- **Attack Direction**: Enemies face targets and use appropriate attack animations
- **Damage Preservation**: Hit animations preserve facing direction after completion
- **State Transitions**: Smooth animation transitions between AI states

## Enemy Types & Characteristics

### Implemented Types
- **Goblin (Melee)**: Fully implemented territorial close-combat enemy with charging behavior, area attacks, and building targeting
  - Can be either Territorial or Aggressive based on spawn conditions
  - 180-degree arc attacks affecting player and buildings
  - Charging ability for gap closing
  - Complete animation system with directional facing
  - Building targeting with priority-based selection
- **Future Types**: Archer (ranged), Necromancer (summoner), Shaman (support), etc.

### Extensible Design
- **EnemyType Enum**: Centralized type definitions with stat extensions
- **Stat System**: Health, damage, speed, detection range, attack range, XP rewards
- **Behavioral Variants**: Different AI patterns per enemy type while sharing base systems

## Spawning & Region Management

### Region Integration
- **Region5Manager.cs**: Complete implementation handling enemy lifecycle in Region 5
- **Spawn Limits**: 2 enemies maximum per region with distance-based collision avoidance
- **Territory Assignment**: Enemies assigned to EnemyTerritory areas for coordinated behavior
- **Spawn Validation**: Checks for valid tiles, existing enemy proximity, and static object collision

### Save/Load System
- **EnemySaveData.cs**: Complete persistence structure for enemy state, position, health, territory, AND behavior type
- **ResourcesManager.cs**: Extended with enemy data serialization using GlobalJsonOptions
- **Region Persistence**: Enemies saved per region with full state restoration on load
- **Behavior Persistence**: Territorial vs Aggressive state maintained across save/load cycles

## Combat Integration

### Combat Target System
- **ICombatTarget Interface**: Fully implemented on all production buildings (Anvil, Campfire, Furnaces, Grindstone, Workbench, SeedMaker)
- **Target Priority System**: Player (100) > Combat Buildings (80) > Production Buildings (60) > Walls (40)
- **Building Detection**: Enemies automatically detect and target buildings within detection range
- **Damage Integration**: Buildings receive damage through ICombatTarget.OnAttacked() method using existing IDestroyableObject system
- **Attack Mechanics**: 180-degree arc attacks affect both player and buildings in range
- **Smart Targeting**: Enemies select best target based on priority and distance using pathfinding-aware selection

### Signal System
- **CommonSignals.cs**: Extended with enemy-specific events for combat coordination
- **Event Integration**: Enemy attacks, defeats, and spawns broadcast to game systems
- **Combat Feedback**: Damage numbers, sound effects, and visual feedback integration

## Performance & Optimization

### Update Management
- **Distance-Based Updates**: Only enemies near player receive full AI updates
- **Spatial Partitioning**: Grid-based system for efficient enemy queries and collision detection
- **State Optimization**: Distant enemies use simplified state updates

### Memory Management
- **Object Pooling**: Reuse enemy instances and projectiles to reduce garbage collection
- **Efficient Pathfinding**: Cached paths and simplified navigation for distant enemies
- **Resource Cleanup**: Proper disposal of enemy resources on death or region unload

## Technical Implementation Notes

### Integration Requirements
- **Collision Layers**: Enemies use dedicated collision layers separate from player and buildings
- **Signal Architecture**: All enemy events broadcast through CommonSignals for loose coupling
- **Save Compatibility**: Enemy data integrates with existing GameSaveData structure including behavior type persistence
- **Region Coordination**: Enemies managed by region-specific managers following established patterns

### Error Handling & Robustness
- **Null Safety**: All target and node references validated before use
- **Pathfinding Fallbacks**: Simple direct movement when complex pathfinding fails for aggressive enemies
- **State Recovery**: Enemies can recover from invalid states and positions
- **Save Data Validation**: Corrupted enemy data handled gracefully with defaults
- **Behavior Validation**: Invalid behavior types default to Territorial

## Current Implementation Status

### Completed Features
- **Base Enemy System**: Complete AI state machine with patrolling, pursuing, attacking, returning, and stunned states
- **Melee Goblin**: Fully functional territorial enemy with charging mechanics and area attacks
- **Building Targeting**: All production buildings implement ICombatTarget interface and can be targeted by enemies
- **Priority System**: Smart target selection based on target type priority and distance
- **Combat Integration**: Enemies can damage both player and buildings using consistent damage systems
- **Animation System**: Complete directional animation support with proper state transitions
- **Save/Load System**: Full persistence of enemy state, health, position, and territory assignments
- **Region Integration**: Enemies properly managed by region-specific managers with spawn limits

### Needs Implementation/Enhancement
- **Behavioral System**: Clear distinction between Territorial and Aggressive enemy types
- **Enhanced Pursuit Logic**: Territorial enemies need ability to leave territory during pursuit
- **Pathfinding System**: Aggressive enemies need obstacle-aware pathfinding
- **Target Scanning**: Intelligent target detection and switching system
- **Behavior Persistence**: Save/load support for enemy behavior types

### Pending Implementation
- **Additional Enemy Types**: Archer, Necromancer, Shaman, and boss enemies
- **Advanced AI Behaviors**: Group coordination, advanced combat tactics
- **Combat Buildings**: Defensive structures that can fight back against enemies
- **Wall System**: Defensive barriers with proper ICombatTarget implementation

## Monument-Based Spawning System (Region 8)

### Overview
Region 8 introduces an advanced monument-based enemy spawning system that provides dynamic enemy generation with destructible spawn points and behavioral switching mechanics.

### Monument System Components
- **EnemySpawner.cs**: Core spawning logic with monument health, destruction/restoration mechanics
- **EnemySpawnRegion8.tscn**: Scene containing animated fireplaces, spawn monuments, and collision systems
- **Region8Manager.cs**: Extended region manager with monument-specific enemy spawning and behavior switching

### Monument Mechanics
- **Health System**: Monuments start with 50 HP and display health bars when damaged
- **Combat Detection**: Monuments take damage from sword attacks (2 damage) and bow arrows (3 damage)
- **Destruction State**: When destroyed, monuments hide active sprites, show destroyed sprite, and stop spawning
- **Restoration Timer**: Destroyed monuments automatically restore after 8 minutes
- **Collision Management**: Different collision shapes for active vs destroyed states

### Spawning Behavior
- **Spawn Rate**: 1 goblin every 2 minutes when monument is active
- **Maximum Enemies**: Up to 6 goblins can be spawned per monument
- **Spawn Area**: Random positions within defined EnemySpawnArea bounds
- **Territory Assignment**: Spawned enemies are assigned to closest available territory

### Behavioral Switching (Key Enhancement)
- **Initial State**: All spawned goblins start as territorial (defensive)
- **Aggressive Conversion**: When 6 goblins are spawned, 4 automatically become aggressive (offensive)
- **Behavior Balance**: Maintains 4 aggressive + 2 territorial enemies at maximum capacity
- **Dynamic Updates**: Behavior switching occurs automatically as enemies are spawned
- **Enhanced Behavior**: 
  - Territorial enemies can now leave territory during pursuit but return when target lost
  - Aggressive enemies roam freely with no territory restrictions and use pathfinding

### Save/Load Integration
- **Monument State**: Health, destruction status, and restoration timers persist across sessions
- **Enemy Persistence**: Spawned enemies maintain their territorial/aggressive states through save/load
- **Timer Continuity**: Restoration timers continue counting during save/load cycles

### Animation System
- **Fireplace Animations**: Continuous animated fireplaces provide visual atmosphere
- **Monument Animations**: Active monuments have animated sprites that stop when destroyed
- **State Transitions**: Smooth visual transitions between active and destroyed states

## System Design Philosophy

### Territorial AI Concept
The enemy system is built around dual-behavior territorial AI where enemies can be either defensive (territorial) or offensive (aggressive). Territorial enemies defend specific areas but can pursue targets beyond their territory when engaged. Aggressive enemies roam freely seeking targets with no territorial restrictions.

### Modular Architecture
The system uses a modular design where base functionality is shared through BaseEnemy.cs while specific behaviors are implemented in derived classes like MeleeGoblin.cs. Behavior switching allows the same enemy type to exhibit different strategic patterns.

### Integration-First Design
Rather than being a standalone system, the enemy architecture is designed to integrate seamlessly with existing game systems including region management, save/load, combat mechanics, and UI systems. This ensures consistency with the overall game architecture.