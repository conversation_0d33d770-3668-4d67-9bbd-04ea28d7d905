using Godot;
using System.Collections.Generic;

public partial class Region10Boss : CharacterBody2D
{
	[Export] public PackedScene ProjectileScene { get; set; }
	[Export] public float MovementSpeed { get; set; } = 50.0f;
	[Export] public float AttackRange { get; set; } = 300.0f;
	[Export] public float AttackCooldown { get; set; } = 4.0f;
	[Export] public int MaxHealth { get; set; } = 125;
	[Export] public int ProjectileDamage { get; set; } = 2;

	[Export] public Node2D SpawnPosition { get; set; }
	[Export] public Node2D Spot1 { get; set; }
	[Export] public Node2D Spot2 { get; set; }
	[Export] public Node2D Spot3 { get; set; }
	[Export] public Node2D Spot4 { get; set; }
	[Export] public Node2D Spot5 { get; set; }
	
	[Signal] public delegate void BossDefeatedEventHandler();
	
	private Sprite2D _sprite;
	private ProgressBar _healthBar;
	private Area2D _hitbox;
	private Timer _attackTimer;
	private Timer _movementTimer;
	
	public int _currentHealth;
	private Node2D[] _movementSpots;
	private int _currentSpotIndex = 0;
	private Vector2 _targetPosition;
	private bool _isMoving = false;
	private bool _canAttack = true;
	private PlayerController _player;
	private AnimationPlayer _animationPlayer;
	private bool _hasMovedToSpot1 = false;
	private bool _hasMovedToSpot2 = false;
	private bool _hasMovedToSpot3 = false;
	private bool _hasMovedToSpot4 = false;
	private bool _hasMovedToSpot5 = false;
	private bool _isActive = false;
	
	public override void _Ready()
	{
		_sprite = GetNode<Sprite2D>("Sprite2D");
		_healthBar = GetNode<ProgressBar>("ProgressBar");
		_hitbox = GetNode<Area2D>("Hitbox");
		_attackTimer = GetNode<Timer>("AttackTimer");
		_movementTimer = GetNode<Timer>("MovementTimer");
		_animationPlayer = GetNode<AnimationPlayer>("AnimationPlayer");

		_currentHealth = MaxHealth;
		_healthBar.SetProgress(1.0f);

		_player = GetNode<PlayerController>("/root/world/Player");

		InitializeMovementSpots();

		_attackTimer.WaitTime = AttackCooldown;
		_attackTimer.Timeout += OnAttackTimerTimeout;

		// Connect to sword signal
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.SwordUsed += OnSwordUsed;
		}

		// Boss starts inactive - will be activated when spawned
		_isActive = false;
	}

	public void ActivateBoss()
	{
		_isActive = true;

		// Re-initialize movement spots now that they are assigned
		InitializeMovementSpots();

		// Start movement to spot1 after 3 seconds
		GetTree().CreateTimer(3.0f).Timeout += () => {
			if (IsInstanceValid(this) && _isActive)
			{
				_hasMovedToSpot1 = true;
				MoveToSpot(0); // Move to Spot1 (index 0)
				GD.Print("Region10Boss: Starting movement to Spot1");
			}
		};
	}
	
	private void InitializeMovementSpots()
	{
		_movementSpots = new Node2D[]
		{
			Spot1,
			Spot2,
			Spot3,
			Spot4,
			Spot5
		};

		if (Spot1 != null)
		{
			_targetPosition = Spot1.GlobalPosition;
		}
	}
	
	public override void _PhysicsProcess(double delta)
	{
		if (!_isActive) return;

		if (_isMoving)
		{
			MoveTowardsTarget((float)delta);
		}

		CheckHealthThresholds();

		if (_canAttack && _player != null)
		{
			var distanceToPlayer = GlobalPosition.DistanceTo(_player.GlobalPosition);
			if (distanceToPlayer <= AttackRange)
			{
				AttackPlayer();
			}
		}
	}
	
	private void MoveTowardsTarget(float delta)
	{
		var direction = (_targetPosition - GlobalPosition).Normalized();
		Velocity = direction * MovementSpeed;

		// Play appropriate movement animation based on direction
		PlayMovementAnimation(direction);

		MoveAndSlide();

		if (GlobalPosition.DistanceTo(_targetPosition) < 10.0f)
		{
			_isMoving = false;
			Velocity = Vector2.Zero;

			// Play idle animation facing player
			PlayIdleAnimationFacingPlayer();
		}
	}
	

	
	private void AttackPlayer()
	{
		if (!_canAttack || _player == null || !_isActive)
			return;

		_canAttack = false;


		var projectile = ProjectileScene.Instantiate<Region10BossProjectile>();
		GetParent().AddChild(projectile);
		projectile.GlobalPosition = GlobalPosition;
		projectile.SetTarget(_player.GlobalPosition);
		projectile.Damage = ProjectileDamage;

		_attackTimer.Start();
	}
	
	private void OnAttackTimerTimeout()
	{
		_canAttack = true;

	}
	
	private void CheckHealthThresholds()
	{
		float healthPercent = (float)_currentHealth / MaxHealth;

		// 80% HP threshold - move to spot2
		if (healthPercent <= 0.8f && !_hasMovedToSpot2 && _hasMovedToSpot1)
		{
			_hasMovedToSpot2 = true;
			SpawnSerpentFlies(2);
			MoveToSpot(1); // Spot2 (index 1)
		}
		// 60% HP threshold - move to spot3
		else if (healthPercent <= 0.6f && !_hasMovedToSpot3 && _hasMovedToSpot2)
		{
			_hasMovedToSpot3 = true;
			SpawnSerpentFlies(3);
			MoveToSpot(2); // Spot3 (index 2)
		}
		// 40% HP threshold - move to spot4
		else if (healthPercent <= 0.4f && !_hasMovedToSpot4 && _hasMovedToSpot3)
		{
			_hasMovedToSpot4 = true;
			SpawnSerpentFlies(4);
			MoveToSpot(3); // Spot4 (index 3)
		}
		// 20% HP threshold - move to spot5
		else if (healthPercent <= 0.2f && !_hasMovedToSpot5 && _hasMovedToSpot4)
		{
			_hasMovedToSpot5 = true;
			SpawnSerpentFlies(5);
			MoveToSpot(4); // Spot5 (index 4)
		}
	}

	private void SpawnSerpentFlies(int count)
	{
		var serpentFlyScene = GD.Load<PackedScene>("res://scenes/regions/region10/Region10SerpentFly.tscn");

		// Base position: 32px below boss
		var baseY = GlobalPosition.Y + 32;

		for (int i = 0; i < count; i++)
		{
			var serpentFly = serpentFlyScene.Instantiate<Region10SerpentFly>();
			GetParent().AddChild(serpentFly);

			Vector2 spawnPos;

			if (count == 1)
			{
				// Single serpent: center below boss
				spawnPos = new Vector2(GlobalPosition.X, baseY);
			}
			else
			{
				// Multiple serpents: spread horizontally with 10px spacing
				float totalWidth = (count - 1) * 10; // Total width needed
				float startX = GlobalPosition.X - (totalWidth / 2); // Start position (leftmost)
				spawnPos = new Vector2(startX + (i * 10), baseY);
			}

			serpentFly.GlobalPosition = spawnPos;
			GD.Print($"Region10Boss: Spawned serpent {i + 1}/{count} at position {spawnPos}");
		}
	}

	private void MoveToSpot(int spotIndex)
	{
		if (spotIndex < _movementSpots.Length && _movementSpots[spotIndex] != null)
		{
			_currentSpotIndex = spotIndex;
			_targetPosition = _movementSpots[spotIndex].GlobalPosition;
			_isMoving = true;
		}
	}

	private void PlayMovementAnimation(Vector2 direction)
	{
		// Determine which direction animation to play based on movement direction
		if (Mathf.Abs(direction.X) > Mathf.Abs(direction.Y))
		{
			// Moving more horizontally
			if (direction.X > 0)
			{
				_animationPlayer.Play("MoveRight");
			}
			else
			{
				_animationPlayer.Play("MoveLeft");
			}
		}
		else
		{
			// Moving more vertically
			if (direction.Y > 0)
			{
				_animationPlayer.Play("MoveDown");
			}
			else
			{
				_animationPlayer.Play("MoveUp");
			}
		}
	}

	private void PlayIdleAnimationFacingPlayer()
	{
		if (_player == null) return;

		Vector2 directionToPlayer = (_player.GlobalPosition - GlobalPosition).Normalized();

		// Determine which idle animation to play based on direction to player
		if (Mathf.Abs(directionToPlayer.X) > Mathf.Abs(directionToPlayer.Y))
		{
			// Player is more to the side
			if (directionToPlayer.X > 0)
			{
				_animationPlayer.Play("IdleRight");
			}
			else
			{
				_animationPlayer.Play("IdleLeft");
			}
		}
		else
		{
			// Player is more above/below
			if (directionToPlayer.Y > 0)
			{
				_animationPlayer.Play("IdleDown");
			}
			else
			{
				_animationPlayer.Play("IdleUp");
			}
		}
	}


	
	public void TakeDamage(int damage)
	{
		_currentHealth -= damage;
		_healthBar.SetProgress((float)_currentHealth / MaxHealth);
		
		_sprite.Modulate = new Color(1.0f, 0.4f, 0.27f);
		
		var tween = CreateTween();
		tween.TweenProperty(_sprite, "modulate", Colors.White, 0.2f);
		
		if (_currentHealth <= 0)
		{
			Die();
		}
	}
	
	private void OnSwordUsed(Vector2I tilePosition, Vector2 playerPosition, Vector2 attackDirection)
	{
		// Check if boss is in sword attack arc
		if (IsInSwordAttackArc(playerPosition, attackDirection))
		{
			int swordLevel = GameSaveData.Instance.PlayerStats.ToolLevels.TryGetValue(ToolType.Sword, out int level) ? level : 1;
			int damage = swordLevel + 1; // Same as other enemies
			TakeDamage(damage);
		}
	}

	private bool IsInSwordAttackArc(Vector2 playerPosition, Vector2 attackDirection)
	{
		float distance = GlobalPosition.DistanceTo(playerPosition);
		if (distance > 32.0f) return false; // Sword range

		Vector2 directionToTarget = (GlobalPosition - playerPosition).Normalized();
		float dotProduct = attackDirection.Dot(directionToTarget);

		// 180 degree arc
		float threshold = Mathf.Cos(Mathf.Pi / 2.0f);
		return dotProduct >= threshold;
	}

	private void Die()
	{
		EmitSignal(SignalName.BossDefeated);

		// Spawn CopperChest with 250 gold and 10 copper bars
		SpawnRewardChest();

		QueueFree();
	}

	private void SpawnRewardChest()
	{
		// Create resource rewards for the chest
		var rewards = new List<ResourceReward>
		{
			new ResourceReward { ResourceType = ResourceType.CopperBar, Quantity = 10 }
		};

		// Spawn copper chest with 250 gold and 10 copper bars
		var chest = Chest.SpawnChest(GlobalPosition, Chest.ChestType.CopperChest, 250, rewards);

		if (chest != null)
		{
			GD.Print("Boss defeated! Spawned reward chest with 250 gold and 10 copper bars.");
		}
	}

	public override void _ExitTree()
	{
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.SwordUsed -= OnSwordUsed;
		}
	}
}
