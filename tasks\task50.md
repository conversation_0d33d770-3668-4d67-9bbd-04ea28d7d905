TASK-1:
Look at  task59.md where you had instruction how to create a building. Now, create a building called "Windmill". It's image is here: res://resources/cutefantasy/windmill/windmill_base.png. There is also icon for build panel res://resources/cutefantasy/windmill/windmill_icon_16x16.png. Implement it so that i can build and use it just like seed maker. You have guidence in BuildBuilding.md. Add resource type like in task59.md->TASK-1: Flour - and in menu of Windmill that it 1:1 duplicated i want  to have only a single item - create from 2xWheat->flour. Flour texture is here res://resources/solaria/resources/flour.png and icon res://resources/solaria/resources/icons/flour_icon.png. Analyse deeply how building should be added and add it. important thing - windmill is 4 tiles width and 5 tiles height. It's base (where it is placed and blocks ground so that other buildins can't be spawned there) is 4 tiles width and 2 tile height. So it occupies 4x2  tiles but it's 4x5 tiles in size. when player behind windmill - it should be semi transparent like other buildings or a tree). When i place building and move my mouse, i want the center of mouse be on the center of the building width but 1 tile from bottom high so that it looks good when placing. also, the bottom part of this building should occupy those 4x2 fields, not the upper part of the building - so proper offset is probably needed and transform.