using Godot;

public partial class Region10BossProjectile : CharacterBody2D
{
	[Export] public float Speed { get; set; } = 200.0f;
	[Export] public int Damage { get; set; } = 2;
	[Export] public float Lifetime { get; set; } = 5.0f;
	
	private Vector2 _direction;
	private Timer _lifetimeTimer;
	private Area2D _hitbox;
	private Sprite2D _sprite;
	private AnimationPlayer _animationPlayer;
	
	public override void _Ready()
	{
		_hitbox = GetNode<Area2D>("Hitbox");
		_sprite = GetNode<Sprite2D>("Sprite2D");
		_animationPlayer = GetNode<AnimationPlayer>("AnimationPlayer");
		_lifetimeTimer = GetNode<Timer>("LifetimeTimer");

		_hitbox.AreaEntered += OnHitboxAreaEntered;

		_lifetimeTimer.WaitTime = Lifetime;
		_lifetimeTimer.Timeout += OnLifetimeTimeout;
		_lifetimeTimer.Start();

		_animationPlayer.Play("Animate");
	}
	
	public void SetTarget(Vector2 targetPosition)
	{
		_direction = (targetPosition - GlobalPosition).Normalized();
		
		var angle = _direction.Angle();
		Rotation = angle;
	}
	
	public override void _PhysicsProcess(double delta)
	{
		Velocity = _direction * Speed;
		MoveAndSlide();

		// Check if projectile collided with anything
		if (GetSlideCollisionCount() > 0)
		{
			QueueFree();
		}
	}
	
	private void OnHitboxAreaEntered(Area2D area)
	{
		if (area.Name == "PlayerDetector")
		{
			var player = area.GetParent() as PlayerController;
			if (player != null)
			{
				player.TakeDamage(Damage);
				GD.Print($"Region10BossProjectile: Hit player for {Damage} damage");
				QueueFree();
			}
		}
		// Note: We don't need to handle other areas here because we only want to hit the player
		// The CharacterBody2D collision will handle hitting walls and other solid objects
	}
	
	private void OnLifetimeTimeout()
	{
		QueueFree();
	}
}
