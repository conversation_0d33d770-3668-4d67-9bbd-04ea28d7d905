# How to Add a New Building

This document provides a step-by-step guide on how to implement a new building in the game. It covers all the necessary file modifications and describes the changes required to integrate the new building into the existing systems.

---

### 1. Create the Building Scene and Script

- **Create a new `.tscn` file:** This file will represent the visual scene of your new building. It should be placed in the `scenes/mapObjects/buildings/` directory.
- **Create a new C# script:** This script will contain the logic for your new building. It should be placed in the same directory as the `.tscn` file.

### 2. Define the Building's ObjectType

- **File:** `scripts/ObjectType.cs`
- **Action:** Add your new building to the `ObjectType` enum. This will give your building a unique identifier that can be used throughout the codebase.

### 3. Define Building Costs

- **File:** `scenes/UI/build/BuildMenu.cs`
- **Action:** Add a new set of static readonly fields to define the resource costs for constructing your new building. Follow the existing pattern for other buildings.

### 4. Integrate with the Build Menu

- **File:** `scenes/UI/build/BuildMenu.cs`
- **Action:**
    - Add a new `Button` and `Sprite2D` field for your building's build button.
    - In the `_Ready()` method, get the new button and sprite nodes from the scene tree.
    - Create a new `On<YourBuildingName>BuildButtonPressed()` method to handle the button's `Pressed` signal.
    - In the `UpdateBuildButtonStates()` method, add logic to enable or disable the build button based on whether the player can afford the building.
    - Add a new `CanAfford<YourBuildingName>()` method to check if the player has the required resources to build your new building.

### 5. Integrate with the Building Placer

- **File:** `scenes/BuildingPlacer.cs`
- **Action:**
    - Add a new `PackedScene` export for your building's scene.
    - Create a new `StartPlacing<YourBuildingName>()` method to initiate the building placement process.
    - In the `UpdateBuildingPosition()` method, add a new `else if` block to handle the position update for your building.
    - In the `TryPlaceBuilding()` method, add a new `else if` block to call the `PlaceBuilding()` method on your building's script.

### 6. Integrate with the Building Manager

- **File:** `scenes/BuildingManager.cs`
- **Action:**
    - Add a new `PackedScene` field for your building's scene and load it in the `_Ready()` method.
    - In the `LoadBuilding()` method, add a new `case` to the `switch` statement to handle loading your new building from save data.
    - Create a new `Load<YourBuildingName>()` method to instantiate and load your building from the provided `BuildingData`.

### 7. Implement Building Logic

- **File:** `scenes/mapObjects/buildings/<YourBuildingName>.cs`
- **Action:**
    - Implement the `IDestroyableObject` and `ICombatTarget` interfaces if your building can be damaged or destroyed.
    - Implement the `PlaceBuilding()` method to handle the logic of placing the building on the map, including consuming resources and marking tiles as occupied.
    - Implement the `LoadFromSaveData()` method to restore the building's state from a `BuildingData` object.
    - If your building has production capabilities, implement the necessary logic for crafting, smelting, or other production processes. This includes defining recipes, handling timers, and spawning the produced items.
    - Implement any other specific logic for your building, such as UI interactions, animations, or special behaviors.

### 8. Add to Save Data

- **File:** `scripts/GameData.cs`
- **Action:** No direct changes are needed here as long as you use the `BuildingData` class correctly. Ensure that you are calling `ResourcesManager.Instance.AddBuilding()` when your building is placed and `ResourcesManager.Instance.RemoveBuildingById()` when it's destroyed to keep the save data consistent.
