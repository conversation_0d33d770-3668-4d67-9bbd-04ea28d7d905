[gd_scene load_steps=4 format=3 uid="uid://ja0pe7e80nt0"]

[ext_resource type="Script" uid="uid://baq5fq341tbtm" path="res://scenes/mapObjects/SmallCactus.cs" id="1_smallcactus"]
[ext_resource type="Texture2D" uid="uid://pwharsro5eka" path="res://resources/pixel mood/objects/cactus2.png" id="2_smallcactus"]
[ext_resource type="PackedScene" uid="uid://otpfc634hhga" path="res://scenes/UI/progress/ProgressBar.tscn" id="3_xb8u3"]

[node name="SmallCactus" type="Node2D"]
y_sort_enabled = true
script = ExtResource("1_smallcactus")

[node name="Sprite2D" type="Sprite2D" parent="."]
texture = ExtResource("2_smallcactus")

[node name="Area2D" type="Area2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="Area2D"]

[node name="StaticBody2D" type="StaticBody2D" parent="."]

[node name="CollisionPolygon2D" type="CollisionPolygon2D" parent="StaticBody2D"]
polygon = PackedVector2Array(4, 5, -4, 5, -5, 1, 6, 1)

[node name="ProgressBar" parent="." instance=ExtResource("3_xb8u3")]
position = Vector2(0, 7)
scale = Vector2(1, 0.6)
