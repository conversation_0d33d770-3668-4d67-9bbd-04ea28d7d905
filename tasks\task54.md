I want to refactor some aspects of attack system.
<system>Read all files required to properly understand the system. You need to be 100% sure before implementing anything. Read all required files including tscn and cs to understand deeply the system.Don't be affraid to read tscn files, don't avoid them. Prepare detailed implementation plan with required steps and logic before implementing anything.</system>

TASK-1. When i restart game, there are palisades spawned. When they are spawned they are not always correctly connected to each other. I want them to be connected correctly when spawned. To achive this I suggest to perform the same logic like when palisade is built - to inform palisades around that there is a new palisade and they need to adjust their texture.

TASK-2. I added TeslaCoil, TeslaCoilMenu and TeslaCoilBullet.
1. TeslaCoil is a building
2. TeslaCoilMenu is a menu that should be opened when player is in range of tesla coil and clicks Interact key (E currently).
3. TeslaCoilBullet is a bullet that is spawned when enemy is in area of tesla coil.

TeslaCoil should be possible to be build from building menu just like other buildings. It should cost 10 stone bars and 5 planks. It should take 1x2 tiles (1 width 2 height, tile size is 16x16px). Read TeslaCoil.tscn. It has EnemyDetector - which should detect enemy entering tesla range. We currently have only one enemy - MeleeGoblin. It has in it's root node added global group Enemy. Read MeleeGoblin.tscn to see it. So EnemyDetector should detect all enemies. When enemy is detected then you need to spawn TeslaCoilBullet every 3s that moves toward closest enemy. Max 300px then it disappear if not hit anything. You need to check how arrow works and copy similar logic. But for tesla coil bullet we need to work on different layer so that trees and other obstacles do not block tesla coil bullet. Make sure melee goblin takes damage from tesla coil bullet. Use layer 6 for this (collision detection). This should work on AreaEnterd event. Melee enemy should take damage that is equal to tesla coil level. So bullet needs information about damage it should take based on tesla coil level. Make sure layers match (melee goblin and tesla coil bullet). If melee goblin has some layers that he listens on currently (look from tscn and cs) then make sure you add layer 6 there but don't remove other layers. To detect enemies in range of tesla use TeslaCoil->EnemyDetector.

When player is in range of tesla coil (look at player scene to see which node detects area enterd and adjust layer in tesla coil menu to layer that player has -> TeslaCoil->PlayerDetector). Those detections are from AreaEnterd event. So when player is in range, then you need to show KeyE prompt - form common signals. When player clicks E then you need to open tesla coil menu. Tesla coil menu is opened by playing animation "Open" from TeslaCoilMenu->AnimationPlayer. When player clicks Close button in tesla coil menu, then you need to play animation "Close" from TeslaCoilMenu->AnimationPlayer. Initially TeslaCoilMenu->Panel should be hidden.

In menu there are labels. Read their texts and add descriptions. For label LevelText - set text like 
"Level X" where X is level and this Level text needs to be translated. So add for it also translation and use it like Tr(...). Price1/2Text and Price1/2 are text and sprite of price to upgrade to next level. Tesla can be upgraded by clicing on Upgrade button. When you click on it then you need to check if player has enough resources. If not then show not enough resources prompt (from common signals). If yes then subtract resources and upgrade tesla coil. When tesla coil is upgraded then you need to update price and level text. Prices are 10 copper bars 5 planks, 10 iron bars 5 planks, 10 gold bars 5 planks, 10 indigosium bars 5 planks, 10 mithril bars 5 planks, 10 erithrydium bars 5 planks, 10 adamantite bars 5 planks, 10 uranium bars 5 planks. Initially tesla deals 1 damage, +1 each level. Also there is label TextDamage2 and TextDamage3. TextDamage2 should show current damage and TextDamage3 should show damage after upgrade. Upgrade sprites (for resources) you need to take from texturemanager.

TeslaCoil is a building that can be built from building menu. Look how for example campfire is implemented. I want you to apply similar logic to tesla coil -> so you can copy 1:1 this script but adjust for tesla coil as it should attack enemies - as i described. But it's still a building so it's level should be saved/loaded just like other buildings data (other buildings does not have level). So make sure after i save and reload game - tesla coil is loaded, the place on tilemap is reserved, when destroyed - released. In build menu you need to duplicate last item list and add tesla coil to it. So you need to read that tscn and adjust it. Also make sure you add all other required logic to build/handle tesla coil building. You can anlyse in depth how SeedMaker works and do it similarly.

TeslaCoilBullet should not be able to damage player. Only enemies. And should not stop on trees and other objects as i already described. When it hits wnemy, enemy should take damage and bullet should disappear.

Tasla coil has health bar (ProgressBar) that should be visible if tesla hp is less than max hp.

TeslaCoilBullet should move in speed like arrow (but i want to be able to adjust it in inspector).

TeslaCoilMenu should be registered just like other menus (for example CampfireMenu). Understand how this is done and apply similar logic to tesla coil menu.

TASK-3:
Make sure project builds after you complete task 2.