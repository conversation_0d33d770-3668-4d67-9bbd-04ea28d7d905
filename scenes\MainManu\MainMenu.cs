using Godot;
using System;

public partial class MainMenu : Node2D
{
	private AnimationPlayer _animationPlayer;
	private Sprite2D _selectSavePanel;
	private Button _playButton;
	private Button _quitButton;
	private Button _closeButton;
	
	private Button[] _saveButtons = new Button[4];
	private Node2D[] _saveNodes = new Node2D[4];
	private Sprite2D[] _newSaveIcons = new Sprite2D[4];
	private Label[] _newSaveLabels = new Label[4];
	private Label[] _levelLabels = new Label[4];
	private Label[] _timeLabels = new Label[4];
	private Sprite2D[] _yesIcons = new Sprite2D[4];
	private Sprite2D[] _timeIcons = new Sprite2D[4];

	public override void _Ready()
	{
		GetNodeReferences();
		ConnectSignals();
		InitializeSavePanel();
		UpdateSaveSlotDisplays();
	}

	private void GetNodeReferences()
	{
		_animationPlayer = GetNode<AnimationPlayer>("AnimationPlayer");
		_selectSavePanel = GetNode<Sprite2D>("CanvasLayer/Control/SelectSavePanel");
		_playButton = GetNode<Button>("CanvasLayer/Control/Buttons/PlayButton");
		_quitButton = GetNode<Button>("CanvasLayer/Control/Buttons/QuitButton");
		_closeButton = GetNode<Button>("CanvasLayer/Control/SelectSavePanel/CloseButton");

		for (int i = 0; i < 4; i++)
		{
			string saveNodePath = $"CanvasLayer/Control/SelectSavePanel/SaveSelect/Save{i + 1}";
			_saveNodes[i] = GetNode<Node2D>(saveNodePath);
			_saveButtons[i] = GetNode<Button>($"{saveNodePath}/SelectButton");
			_newSaveIcons[i] = GetNode<Sprite2D>($"{saveNodePath}/NewSaveIcon");
			_newSaveLabels[i] = GetNode<Label>($"{saveNodePath}/NewSaveLabel");
			_levelLabels[i] = GetNode<Label>($"{saveNodePath}/LevelLabel");
			_timeLabels[i] = GetNode<Label>($"{saveNodePath}/TimeLabel");
			_yesIcons[i] = GetNode<Sprite2D>($"{saveNodePath}/YesIcon");
			_timeIcons[i] = GetNode<Sprite2D>($"{saveNodePath}/TimeIcon");
		}
	}

	private void ConnectSignals()
	{
		_playButton.Pressed += OnPlayButtonPressed;
		_quitButton.Pressed += OnQuitButtonPressed;
		_closeButton.Pressed += OnCloseButtonPressed;

		for (int i = 0; i < 4; i++)
		{
			int saveSlot = i + 1;
			_saveButtons[i].Pressed += () => OnSaveSlotSelected(saveSlot);
		}
	}

	private void InitializeSavePanel()
	{
		_selectSavePanel.Visible = false;
	}

	private void UpdateSaveSlotDisplays()
	{
		for (int i = 0; i < 4; i++)
		{
			int saveSlot = i + 1;
			string saveFileName = $"save{saveSlot}";
			bool saveExists = SaveHandler.SaveExists(saveFileName);

			if (saveExists)
			{
				ShowExistingSave(i, saveSlot);
			}
			else
			{
				ShowNewSave(i);
			}
		}
	}

	private void ShowExistingSave(int index, int saveSlot)
	{
		_newSaveIcons[index].Visible = false;
		_newSaveLabels[index].Visible = false;
		_levelLabels[index].Visible = true;
		_timeLabels[index].Visible = true;
		_yesIcons[index].Visible = true;
		_timeIcons[index].Visible = true;

		var saveData = SaveHandler.Load<GameSaveData>($"save{saveSlot}");
		if (saveData != null && saveData.IsValid())
		{
			_levelLabels[index].Text = $"LVL {saveData.PlayerStats.Level}";

			int totalSeconds = saveData.PlayTimeSeconds;
			int hours = totalSeconds / 3600;
			int minutes = (totalSeconds % 3600) / 60;
			_timeLabels[index].Text = $"{hours}:{minutes:D2}";

			GD.Print($"MainMenu: Loaded save slot {saveSlot} - Level {saveData.PlayerStats.Level}, Time {hours}:{minutes:D2}");
		}
		else
		{
			_levelLabels[index].Text = "LVL 1";
			_timeLabels[index].Text = "0:00";
			GD.Print($"MainMenu: Save slot {saveSlot} exists but data is invalid");
		}
	}

	private void ShowNewSave(int index)
	{
		_newSaveIcons[index].Visible = true;
		_newSaveLabels[index].Visible = true;
		_levelLabels[index].Visible = false;
		_timeLabels[index].Visible = false;
		_yesIcons[index].Visible = false;
		_timeIcons[index].Visible = false;
	}

	private void OnPlayButtonPressed()
	{
		_animationPlayer.Play("OpenLevelSelect");
	}

	private void OnQuitButtonPressed()
	{
		GetTree().Quit();
	}

	private void OnCloseButtonPressed()
	{
		_animationPlayer.Play("CloseLevelSelect");
	}

	private void OnSaveSlotSelected(int saveSlot)
	{
		GD.Print($"MainMenu: Save slot {saveSlot} selected");

		// Set the current save slot
		ResourcesManager.CurrentSaveSlot = saveSlot;

		// Initialize ResourcesManager for gameplay with the selected save slot
		ResourcesManager.Instance.InitializeForGameplay();

		// Change to world scene
		GetTree().ChangeSceneToFile("res://scenes/world.tscn");
	}
}
