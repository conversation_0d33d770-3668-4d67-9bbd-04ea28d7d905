import os

def count_cs_lines(start_dir):
    total_lines = 0
    total_files = 0

    for root, _, files in os.walk(start_dir):
        for file in files:
            if file.endswith('.cs') or file.endswith('.tscn'):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        line_count = sum(1 for _ in f)
                        total_lines += line_count
                        total_files += 1
                        print(f"{file_path}: {line_count} lines")
                except Exception as e:
                    print(f"Error reading {file_path}: {e}")

    print(f"\nTotal .cs files: {total_files}")
    print(f"Total lines of code: {total_lines}")

if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1:
        start_directory = sys.argv[1]
    else:
        start_directory = input("Enter the starting directory: ")

    if not os.path.isdir(start_directory):
        print("Directory does not exist.")
    else:
        count_cs_lines(start_directory)
