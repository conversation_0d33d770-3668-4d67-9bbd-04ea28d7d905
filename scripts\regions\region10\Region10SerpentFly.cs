using Godot;

public partial class Region10SerpentFly : CharacterBody2D
{
	[Export] public float MovementSpeed { get; set; } = 50.0f;
	[Export] public float AttackRange { get; set; } = 18.0f;
	[Export] public float AttackCooldown { get; set; } = 3.5f;
	[Export] public int MaxHealth { get; set; } = 2;
	[Export] public int AttackDamage { get; set; } = 1;
	[Export] public float PlayerDistanceThreshold { get; set; } = 16.0f;
	
	private Sprite2D _sprite;
	private AnimationPlayer _animationPlayer;
	private ProgressBar _healthBar;
	private Area2D _hitbox;
	private Area2D _attackArea;
	private Timer _attackTimer;

	private int _currentHealth;
	private bool _canAttack = true;
	private PlayerController _player;
	private bool _isAttacking = false;
	private bool _hasReachedPlayer = false;
	private float _attackDelayTimer = 0.0f;
	private bool _isMoving = false;
	
	public override void _Ready()
	{
		_sprite = GetNode<Sprite2D>("Sprite2D");
		_animationPlayer = GetNode<AnimationPlayer>("AnimationPlayer");
		_healthBar = GetNode<ProgressBar>("ProgressBar");
		_hitbox = GetNode<Area2D>("Hitbox");
		_attackArea = GetNode<Area2D>("AttackArea");
		_attackTimer = GetNode<Timer>("AttackTimer");

		_currentHealth = MaxHealth;
		_healthBar.SetProgress(1.0f);
		_healthBar.Visible = false;

		_player = GetNode<PlayerController>("/root/world/Player");
		
		_attackTimer.WaitTime = AttackCooldown;
		_attackTimer.Timeout += OnAttackTimerTimeout;

		// Connect to sword signal
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.SwordUsed += OnSwordUsed;
		}

		// Connect to animation finished signal
		_animationPlayer.AnimationFinished += OnAnimationFinished;

		// Start with Appear animation
		_animationPlayer.Play("Appear");
	}
	
	public override void _PhysicsProcess(double delta)
	{
		if (_player == null || _isAttacking) return;

		float distanceToPlayer = GlobalPosition.DistanceTo(_player.GlobalPosition);

		// Handle attack delay timer
		if (_hasReachedPlayer && _attackDelayTimer > 0)
		{
			_attackDelayTimer -= (float)delta;
			if (_attackDelayTimer <= 0 && _canAttack)
			{
				AttackPlayer();
			}
		}

		// Movement logic
		if (distanceToPlayer > PlayerDistanceThreshold)
		{
			// Move towards player
			var direction = (_player.GlobalPosition - GlobalPosition).Normalized();
			Velocity = direction * MovementSpeed;
			MoveAndSlide();

			// Play movement animation based on direction
			if (!_isMoving)
			{
				_isMoving = true;
				PlayMovementAnimation(direction);
			}

			_hasReachedPlayer = false;
		}
		else
		{
			// Stop moving when within threshold
			Velocity = Vector2.Zero;

			// Play idle animation if was moving
			if (_isMoving)
			{
				_isMoving = false;
				PlayIdleAnimationFacingPlayer();
			}

			// Start attack delay if just reached player
			if (!_hasReachedPlayer)
			{
				_hasReachedPlayer = true;
				_attackDelayTimer = 1.0f; // 1 second delay before attack
			}
		}
	}

	private void AttackPlayer()
	{
		if (_player == null || !_canAttack || _isAttacking) return;

		float distanceToPlayer = GlobalPosition.DistanceTo(_player.GlobalPosition);
		if (distanceToPlayer <= AttackRange)
		{
			_isAttacking = true;
			_canAttack = false;
			_attackTimer.Start();

			// Play attack animation based on direction to player
			PlayAttackAnimationFacingPlayer();
		}
	}
	
	private void OnAttackTimerTimeout()
	{
		_canAttack = true;
	}

	private void OnSwordUsed(Vector2I tilePosition, Vector2 playerPosition, Vector2 attackDirection)
	{
		// Check if serpent fly is in sword attack arc
		if (IsInSwordAttackArc(playerPosition, attackDirection))
		{
			int swordLevel = GameSaveData.Instance.PlayerStats.ToolLevels.TryGetValue(ToolType.Sword, out int level) ? level : 1;
			int damage = swordLevel + 1; // Same as other enemies
			TakeDamage(damage);
		}
	}

	private bool IsInSwordAttackArc(Vector2 playerPosition, Vector2 attackDirection)
	{
		float distance = GlobalPosition.DistanceTo(playerPosition);
		if (distance > 32.0f) return false; // Sword range

		Vector2 directionToTarget = (GlobalPosition - playerPosition).Normalized();
		float dotProduct = attackDirection.Dot(directionToTarget);

		// 180 degree arc
		float threshold = Mathf.Cos(Mathf.Pi / 2.0f);
		return dotProduct >= threshold;
	}
	
	public void TakeDamage(int damage)
	{
		_currentHealth -= damage;
		_healthBar.SetProgress((float)_currentHealth / MaxHealth);
		_healthBar.Visible = true;

		// Play hit animation based on direction to player
		PlayHitAnimationFacingPlayer();

		_sprite.Modulate = new Color(1.0f, 0.4f, 0.27f);

		var tween = CreateTween();
		tween.TweenProperty(_sprite, "modulate", Colors.White, 0.2f);

		if (_currentHealth <= 0)
		{
			Die();
		}
	}
	
	private void Die()
	{
		// Drop 1 coin as specified in task
		DroppedResource.SpawnCoin(GlobalPosition, 1);

		QueueFree();
	}

	private void OnAnimationFinished(StringName animName)
	{
		if (animName.ToString() == "Appear")
		{
			// After appear animation, start with idle
			PlayIdleAnimationFacingPlayer();
		}
		else if (animName.ToString().StartsWith("Attack"))
		{
			// After attack animation, damage player and return to idle
			if (_player != null)
			{
				float distanceToPlayer = GlobalPosition.DistanceTo(_player.GlobalPosition);
				if (distanceToPlayer <= AttackRange)
				{
					_player.TakeDamage(AttackDamage);
				}
			}
			_isAttacking = false;
			PlayIdleAnimationFacingPlayer();
		}
		else if (animName.ToString().StartsWith("Hit"))
		{
			// After hit animation, return to appropriate state
			if (_isMoving)
			{
				var direction = (_player.GlobalPosition - GlobalPosition).Normalized();
				PlayMovementAnimation(direction);
			}
			else
			{
				PlayIdleAnimationFacingPlayer();
			}
		}
	}

	private void PlayMovementAnimation(Vector2 direction)
	{
		// Determine which direction animation to play based on movement direction
		if (Mathf.Abs(direction.X) > Mathf.Abs(direction.Y))
		{
			// Moving more horizontally
			if (direction.X > 0)
			{
				_animationPlayer.Play("MoveRight");
			}
			else
			{
				_animationPlayer.Play("MoveLeft");
			}
		}
		else
		{
			// Moving more vertically
			if (direction.Y > 0)
			{
				_animationPlayer.Play("MoveDown");
			}
			else
			{
				_animationPlayer.Play("MoveUp");
			}
		}
	}

	private void PlayIdleAnimationFacingPlayer()
	{
		if (_player == null) return;

		Vector2 directionToPlayer = (_player.GlobalPosition - GlobalPosition).Normalized();

		// Determine which idle animation to play based on direction to player
		if (Mathf.Abs(directionToPlayer.X) > Mathf.Abs(directionToPlayer.Y))
		{
			// Player is more to the side
			if (directionToPlayer.X > 0)
			{
				_animationPlayer.Play("IdleRight");
			}
			else
			{
				_animationPlayer.Play("IdleLeft");
			}
		}
		else
		{
			// Player is more above/below
			if (directionToPlayer.Y > 0)
			{
				_animationPlayer.Play("IdleDown");
			}
			else
			{
				_animationPlayer.Play("IdleUp");
			}
		}
	}

	private void PlayAttackAnimationFacingPlayer()
	{
		if (_player == null) return;

		Vector2 directionToPlayer = (_player.GlobalPosition - GlobalPosition).Normalized();

		// Determine which attack animation to play based on direction to player
		if (Mathf.Abs(directionToPlayer.X) > Mathf.Abs(directionToPlayer.Y))
		{
			// Player is more to the side
			if (directionToPlayer.X > 0)
			{
				_animationPlayer.Play("AttackRight");
			}
			else
			{
				_animationPlayer.Play("AttackLeft");
			}
		}
		else
		{
			// Player is more above/below
			if (directionToPlayer.Y > 0)
			{
				_animationPlayer.Play("AttackDown");
			}
			else
			{
				_animationPlayer.Play("AttackUp");
			}
		}
	}

	private void PlayHitAnimationFacingPlayer()
	{
		if (_player == null) return;

		Vector2 directionToPlayer = (_player.GlobalPosition - GlobalPosition).Normalized();

		// Determine which hit animation to play based on direction to player
		if (Mathf.Abs(directionToPlayer.X) > Mathf.Abs(directionToPlayer.Y))
		{
			// Player is more to the side
			if (directionToPlayer.X > 0)
			{
				_animationPlayer.Play("HitRight");
			}
			else
			{
				_animationPlayer.Play("HitLeft");
			}
		}
		else
		{
			// Player is more above/below
			if (directionToPlayer.Y > 0)
			{
				_animationPlayer.Play("HitDown");
			}
			else
			{
				_animationPlayer.Play("HitUp");
			}
		}
	}

	public override void _ExitTree()
	{
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.SwordUsed -= OnSwordUsed;
		}

		if (_animationPlayer != null)
		{
			_animationPlayer.AnimationFinished -= OnAnimationFinished;
		}
	}
}
