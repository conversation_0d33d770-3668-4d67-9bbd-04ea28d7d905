maszyna dedykowana do tworzenia 'power source' - z drewna, węgla etc

seed maker
food and drinks - (chees press, mayo maker, oil maker, smoker, beczka, jam maker, beczka(storage?))
crafting: charcol clin, furnace, crystal maker machine, lighting rod, solar panel, recycler, bone mill, rock crusher
potion maker




TOFIX
add light point to furnace
fix day night - icon not fit real time

TODO
craft keys in furnace/anvil
[X-shop added]add option to generate coins (shop? gold stones?)
add more resources and icons
add fish cage ("crab cage")
add planting - planting system, watering system, seed maker, plants, cooking - food options
add workshop where player can build furniture/defence items
add build menu (panel with settings of building)

'master' current regions to region 4 and adjust 
TODO later:
1) next regions
2) monuments (pyramide, dungeons)
3) enemy bases - może wrogowie nachodzą do czasu aż nie pokonasz bosa?
4) bosses
5) defence towers
6) new weapons

!!! grindstone in build menu - add prices

!!! workbench - fix; shop - fix; add fshing; show button hints
!!! planting - add watering system, add seed maker, add other buildings to process food (młyn?)
!!! planting - add health/food/water from fruits, from rabbit baked lag too
!!! planting - seed making: 'base seed' + 'leafe/...' -> concrete seed
!!! next: enemies, next regions with monuments and caves, add quests

!!! workshop - poza ulepszeniem narzedzi, dodaj ulepszenie pochodni gracza
!!! podłoga - tam nie można spawnować obiektow

!!! build menu, mode details, can craft multiple items same type when selected

!!! furnace2 can produce furnace1+furnace2, furnace3 can produce f1+f2+f3
!!! f1 - iron, copper (iron->gwozdzie);
!!! f2 - gold, indigosium
!!! f3 - mithril, erithrydium
!!! f4 - adamantite, uranium

Challenges/Puzzle:
Water flow chellange or light chellange - 

I = In tasks
X = In progress
D = Done

FIXES:
GLOBAL:
          Set proper amount of resources that player gets.
          [D?] Set proper food/water amount
          Set for regions 1-10 tilemap layer data
          Verify if region managers work fine
          Adjust water blockers
          Set max objects (spawned) in each region
          Add rabbits to other screens
          Fix rabbits not moving initially
          Verify - anvil & campfire menus not showing titles, probably not showing all the texts in descriptions
          Verify - Tree2 incorrect position (too high)
          Replace orc with solaria orc
          Verify if statue works, adjust it
          [D] When i click b and build something then i click b - build menu is not opening. but when i click b again - it opens. i want it to open when i build something and click b again. when i select item to build, menu disapears but probably something is not updated and when i click b again it's not opening because it is trying to close first and this is why i suspect.
          Build menu - add prices
          [I/D] Melee Goblin - less attack, less hp
          [D] Bridge cann't be built on water but on other places can - fix
          [D] Text says "Click R when you are close to fireplace" - should be E
          Fix stone y sort
          [I/D] I completed quest (npc), region 4 was unlocked but not on map panel - fix (cant unlock region 5). Actually can after restart game
          Fix new buildings (colliders etc).
          Fix coins should be possible to collect even if player has no space in equipment
          Palisades - fix sometimes they dont merge correctly
REGION-1: [D] Change NPC image
          [D] Verify once more if npc works fine          
REGION-2: In quest board - adjust quests
REGION-3: (home) - adjust statue images, handle boosts of selected statue
REGION-4: [D] Market - in market, verify if all works, if button 'clicks', if all is refreshing etc - can add music etc
REGION-5: 
REGION-6: 
REGION-7: Are enemies spawned? Something is in region manager.
REGION-8: Add some info that player can destroy monument (verify if he can and if it works properly)
REGION-9: 
REGION-10: Fix serpent animations/images
           Fix y sort of walls (pionowe)
           Verify if chest presists, if images when not boss killed and when killed

NEW FEATURES:
GLOBAL:
         Add additional button to open bagpack
         Add chest that can be built and that stores items
         Add mill
         Add food related buildings (beczka, chees press, oil maker, smoker, jam maker)
         Add food items (images, values etc)
         Add particles system.
         Maybe add in player information on which region player was lately standing on?
         Add cave in region 7
         Add chests
         Amulet który sprawia, że masz +5 hp shield? (albo z monumentu)
         Buildings E and colliders have to be disabled when building is in progress of placing
         Artichoke -> remove from food seeds, i changed it to cactus sprite

6 caves: "cave", "crypt" "m-crypt" "cute-crypt"

When adding seed maker - it didnt finish last task - spawn timer init in region manager. also, veirfy changes.