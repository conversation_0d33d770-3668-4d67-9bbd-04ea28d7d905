using Godot;

public partial class TeslaCoilMenu : CanvasLayer, IMenu
{
	private AnimationPlayer _animationPlayer;
	private Sprite2D _panel;
	private Button _closeButton;
	private Button _upgradeButton;
	private Label _levelText;
	private Label _upgradeDescription;
	private Label _price1Text;
	private Label _price2Text;
	private Sprite2D _price1Sprite;
	private Sprite2D _price2Sprite;
	private Label _textDamage;
	private Label _textDamage2;
	private Label _textDamage3;

	private TeslaCoil _teslaCoil;

	public override void _Ready()
	{
		_animationPlayer = GetNode<AnimationPlayer>("AnimationPlayer");
		_panel = GetNode<Sprite2D>("Control/Panel");
		_closeButton = GetNode<Button>("Control/Panel/CloseButton");
		_upgradeButton = GetNode<Button>("Control/Panel/UpgradeButton");
		_levelText = GetNode<Label>("Control/Panel/LevelText");
		_upgradeDescription = GetNode<Label>("Control/Panel/UpgradeDescription");
		_price1Text = GetNode<Label>("Control/Panel/Price1Text");
		_price2Text = GetNode<Label>("Control/Panel/Price2Text");
		_price1Sprite = GetNode<Sprite2D>("Control/Panel/Price1");
		_price2Sprite = GetNode<Sprite2D>("Control/Panel/Price2");
		_textDamage = GetNode<Label>("Control/Panel/TextDamage");
		_textDamage2 = GetNode<Label>("Control/Panel/TextDamage2");
		_textDamage3 = GetNode<Label>("Control/Panel/TextDamage3");

		if (_closeButton != null)
		{
			_closeButton.Pressed += OnCloseButtonPressed;
		}

		if (_upgradeButton != null)
		{
			_upgradeButton.Pressed += OnUpgradeButtonPressed;
		}

		if (_panel != null)
		{
			_panel.Visible = false;
		}

		MenuManager.Instance?.RegisterMenu("TeslaCoilMenu", this);
	}

	public override void _Input(InputEvent @event)
	{
		if (@event.IsActionPressed("Escape") && _panel != null && _panel.Visible)
		{
			CloseMenu();
		}
	}

	public void SetTeslaCoil(TeslaCoil teslaCoil)
	{
		_teslaCoil = teslaCoil;
	}

	public void OpenMenu(TeslaCoil teslaCoil)
	{
		_teslaCoil = teslaCoil;
		UpdateMenuDisplay();

		// Disable player movement when menu opens
		CommonSignals.Instance?.EmitPlayerMovementEnabled(false);

		if (_animationPlayer != null && _animationPlayer.HasAnimation("Open"))
		{
			_animationPlayer.Play("Open");
		}
		else if (_panel != null)
		{
			_panel.Visible = true;
		}
	}

	public void CloseMenu()
	{
		// Re-enable player movement when menu closes
		CommonSignals.Instance?.EmitPlayerMovementEnabled(true);

		if (_animationPlayer != null && _animationPlayer.HasAnimation("Close"))
		{
			_animationPlayer.Play("Close");
		}
		else if (_panel != null)
		{
			_panel.Visible = false;
		}
	}

	private void UpdateMenuDisplay()
	{
		if (_teslaCoil == null) return;

		UpdateLevelText();
		UpdateDamageDisplay();
		UpdateUpgradePrices();
	}

	private void UpdateLevelText()
	{
		if (_levelText != null && _teslaCoil != null)
		{
			_levelText.Text = Tr("LEVEL_TEXT").Replace("{0}", _teslaCoil.GetLevel().ToString());
		}
	}

	private void UpdateDamageDisplay()
	{
		if (_teslaCoil == null) return;

		if (_textDamage2 != null)
		{
			_textDamage2.Text = _teslaCoil.GetDamage().ToString();
		}

		if (_textDamage3 != null)
		{
			if (_teslaCoil.CanUpgrade())
			{
				_textDamage3.Text = _teslaCoil.GetNextLevelDamage().ToString();
			}
			else
			{
				_textDamage3.Text = "MAX";
			}
		}
	}

	private void UpdateUpgradePrices()
	{
		if (_teslaCoil == null) return;

		if (!_teslaCoil.CanUpgrade())
		{
			if (_price1Text != null) _price1Text.Text = "MAX";
			if (_price2Text != null) _price2Text.Text = "MAX";
			if (_price1Sprite != null) _price1Sprite.Visible = false;
			if (_price2Sprite != null) _price2Sprite.Visible = false;
			if (_upgradeButton != null) _upgradeButton.Disabled = true;
			return;
		}

		var barType = _teslaCoil.GetUpgradeBarType();
		int barCost = _teslaCoil.GetUpgradeBarCost();
		int plankCost = _teslaCoil.GetUpgradePlankCost();

		if (_price1Text != null)
		{
			_price1Text.Text = barCost.ToString();
		}

		if (_price2Text != null)
		{
			_price2Text.Text = plankCost.ToString();
		}

		if (_price1Sprite != null && TextureManager.Instance != null)
		{
			_price1Sprite.Texture = TextureManager.Instance.GetResourceTexture(barType);
			_price1Sprite.Visible = true;
		}

		if (_price2Sprite != null && TextureManager.Instance != null)
		{
			_price2Sprite.Texture = TextureManager.Instance.GetResourceTexture(ResourceType.Plank);
			_price2Sprite.Visible = true;
		}

		if (_upgradeButton != null)
		{
			var resourcesManager = ResourcesManager.Instance;
			bool canAfford = resourcesManager != null &&
							resourcesManager.HasResource(barType, barCost) &&
							resourcesManager.HasResource(ResourceType.Plank, plankCost);
			_upgradeButton.Disabled = !canAfford;
		}
	}

	private void OnCloseButtonPressed()
	{
		MenuManager.Instance?.CloseMenu("TeslaCoilMenu");
	}

	private void OnUpgradeButtonPressed()
	{
		if (_teslaCoil == null) return;

		if (_teslaCoil.TryUpgrade())
		{
			UpdateMenuDisplay();
		}
		else
		{
			GD.Print("TeslaCoilMenu: Not enough resources for upgrade");
		}
	}

	public void OpenMenu()
	{
		if (_teslaCoil != null)
		{
			OpenMenu(_teslaCoil);
		}
	}

	public bool IsMenuOpen()
	{
		return _panel != null && _panel.Visible;
	}

	public override void _ExitTree()
	{
		MenuManager.Instance?.UnregisterMenu("TeslaCoilMenu");
	}
}
