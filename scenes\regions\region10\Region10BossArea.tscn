[gd_scene load_steps=57 format=3 uid="uid://drf1d44krbwgg"]

[ext_resource type="Texture2D" uid="uid://55am24sn1r3q" path="res://resources/solaria/region10boss/spawnBossMonument.png" id="2_ajitm"]
[ext_resource type="Texture2D" uid="uid://dliwrxfel22rc" path="res://resources/solaria/region10boss/spawnBossBorders_right.png" id="2_t3y1d"]
[ext_resource type="Texture2D" uid="uid://dta828x0hlg4x" path="res://resources/solaria/region10boss/spawnBossBorders_up.png" id="3_b2abj"]
[ext_resource type="Texture2D" uid="uid://dj28bkakmx82m" path="res://resources/solaria/region10boss/fireAnimation.png" id="3_jmbks"]
[ext_resource type="Texture2D" uid="uid://btpneg22e5fdv" path="res://resources/solaria/region10boss/spawnBossBorders_down.png" id="4_2turf"]
[ext_resource type="Texture2D" uid="uid://cnqe4myfwx0ti" path="res://resources/exterior/Crow_Flap_Right_16x16.png" id="4_icdtx"]
[ext_resource type="Texture2D" uid="uid://bk6wlf37c73ci" path="res://resources/exterior/Crow_idle_Down_16x16.png" id="5_4tylb"]
[ext_resource type="Texture2D" uid="uid://mg4leffmcb68" path="res://resources/exterior/Crow_Flap_Left_16x16.png" id="6_53oeg"]
[ext_resource type="Texture2D" uid="uid://6osirkqsvls6" path="res://resources/solaria/UI/monument/monumentPanel.png" id="9_71uix"]
[ext_resource type="PackedScene" uid="uid://jc24vgr467xj" path="res://scenes/regions/region10/Region10Boss.tscn" id="9_t3y1d"]
[ext_resource type="PackedScene" uid="uid://brynlg0mgkf76" path="res://scenes/UI/common/Label.tscn" id="10_3snc1"]
[ext_resource type="Texture2D" uid="uid://dvv01dmhem1hh" path="res://resources/solaria/UI/build/button2.png" id="11_uynbj"]
[ext_resource type="Texture2D" uid="uid://nlog824yfe8s" path="res://resources/solaria/planting/crops/crop_2.png" id="12_vxowd"]
[ext_resource type="Texture2D" uid="uid://cdxye6tum1anb" path="res://resources/solaria/UI/inventory/close_button.png" id="13_rbjyc"]
[ext_resource type="Script" uid="uid://cheo4n7244u1y" path="res://scripts/regions/region10/Region10BossArea.cs" id="13_t3y1d"]

[sub_resource type="Animation" id="Animation_jmbks"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Walls:visible")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Walls:modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Color(1, 1, 1, 1)]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("BoardToSpawnBoss/Board:modulate")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Color(1, 1, 1, 1)]
}

[sub_resource type="Animation" id="Animation_ajitm"]
resource_name = "SpawnWalls"
length = 3.0
step = 0.1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Walls:visible")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Walls:modulate")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0, 3),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Color(1, 1, 1, 0), Color(1, 1, 1, 1)]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("BoardToSpawnBoss/Board:modulate")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0, 3),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Color(1, 1, 1, 1), Color(1, 1, 1, 0)]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_q6bgb"]
_data = {
&"RESET": SubResource("Animation_jmbks"),
&"SpawnWalls": SubResource("Animation_ajitm")
}

[sub_resource type="RectangleShape2D" id="RectangleShape2D_53oeg"]
size = Vector2(6, 256)

[sub_resource type="RectangleShape2D" id="RectangleShape2D_t3y1d"]
size = Vector2(6, 246)

[sub_resource type="RectangleShape2D" id="RectangleShape2D_b2abj"]
size = Vector2(234, 7)

[sub_resource type="RectangleShape2D" id="RectangleShape2D_2turf"]
size = Vector2(234, 4)

[sub_resource type="Animation" id="Animation_t3y1d"]
resource_name = "BirdIdle"
length = 0.6
loop_mode = 1
step = 0.1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("../Bird:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2, 0.3, 0.4, 0.5),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1),
"update": 1,
"values": [0, 1, 2, 3, 4, 5]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("../Bird:texture")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [ExtResource("5_4tylb")]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("../Bird:rotation")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [0.0]
}
tracks/3/type = "value"
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/path = NodePath("../Bird:position")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(0, 9)]
}
tracks/4/type = "value"
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/path = NodePath("../Bird:visible")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}

[sub_resource type="Animation" id="Animation_2turf"]
resource_name = "BirdMove"
length = 3.0
step = 0.1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("../Bird:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1, 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 1.7, 1.8, 1.9, 2, 2.1, 2.2, 2.3, 2.4, 2.5, 2.6, 2.7, 2.8, 2.9),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1),
"update": 1,
"values": [0, 1, 2, 3, 4, 5, 0, 1, 2, 3, 4, 5, 0, 1, 2, 3, 4, 5, 0, 1, 2, 3, 4, 5, 0, 1, 2, 3, 4, 5]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("../Bird:texture")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [ExtResource("6_53oeg")]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("../Bird:position")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0, 2.9, 2.90184),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(4, -49), Vector2(258, -50), Vector2(259, -91)]
}
tracks/3/type = "value"
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/path = NodePath("../Bird:rotation")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [3.14159]
}
tracks/4/type = "value"
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/path = NodePath("../Bird:visible")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/keys = {
"times": PackedFloat32Array(2.90665),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}

[sub_resource type="Animation" id="Animation_b2abj"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("../Bird:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [0]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("../Bird:texture")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [ExtResource("4_icdtx")]
}
tracks/2/type = "value"
tracks/2/imported = false
tracks/2/enabled = true
tracks/2/path = NodePath("../Bird:position")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(0, -19)]
}
tracks/3/type = "value"
tracks/3/imported = false
tracks/3/enabled = true
tracks/3/path = NodePath("../Bird:rotation")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [3.14159]
}
tracks/4/type = "value"
tracks/4/imported = false
tracks/4/enabled = true
tracks/4/path = NodePath("../Bird:visible")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_0mfy5"]
_data = {
&"BirdIdle": SubResource("Animation_t3y1d"),
&"BirdMove": SubResource("Animation_2turf"),
&"RESET": SubResource("Animation_b2abj")
}

[sub_resource type="Animation" id="Animation_q6bgb"]
resource_name = "Candles"
length = 0.4
loop_mode = 1
step = 0.1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Candle1:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 1,
"values": [0, 1, 2, 3]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Candle2:frame")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 1,
"values": [0, 1, 2, 3]
}

[sub_resource type="Animation" id="Animation_p7s5t"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Candle1:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [0]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Candle2:frame")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [0]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_icdtx"]
_data = {
&"Candles": SubResource("Animation_q6bgb"),
&"RESET": SubResource("Animation_p7s5t")
}

[sub_resource type="CircleShape2D" id="CircleShape2D_jmbks"]
radius = 15.0

[sub_resource type="Animation" id="Animation_uynbj"]
resource_name = "Close"
length = 0.2
step = 0.01
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("CanvasLayer/Control/Node2D:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.06, 0.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1.2, 1.2), Vector2(0.8, 0.8)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("CanvasLayer/Control/Node2D:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0.2),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}

[sub_resource type="Animation" id="Animation_71uix"]
resource_name = "Open"
length = 0.2
step = 0.01
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("CanvasLayer/Control/Node2D:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.12, 0.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(0.8, 0.8), Vector2(1.2, 1.2), Vector2(1, 1)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("CanvasLayer/Control/Node2D:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}

[sub_resource type="Animation" id="Animation_3snc1"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("CanvasLayer/Control/Node2D:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(0.8, 0.8)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("CanvasLayer/Control/Node2D:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_3snc1"]
_data = {
&"Close": SubResource("Animation_uynbj"),
&"Open": SubResource("Animation_71uix"),
&"RESET": SubResource("Animation_3snc1")
}

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_71uix"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_3snc1"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_uynbj"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_vxowd"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_rbjyc"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_a3oqo"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_y4aaa"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_i7s8p"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_lq6fy"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_1hscm"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_6rggy"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_fo70f"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_8y4xw"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_y327f"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_sa6b4"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_a5it2"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_e5bc7"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_6ehpm"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_ewlc0"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_csrho"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_7rm2h"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_11mag"]

[node name="Region10BossArea" type="Node2D"]
y_sort_enabled = true
script = ExtResource("13_t3y1d")

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_q6bgb")
}

[node name="Walls" type="Sprite2D" parent="."]
y_sort_enabled = true
position = Vector2(0, -1)

[node name="Right" type="Sprite2D" parent="Walls"]
y_sort_enabled = true
position = Vector2(120, 0)
texture = ExtResource("2_t3y1d")

[node name="StaticBody2D" type="StaticBody2D" parent="Walls/Right" groups=["navigation_polygon_source_geometry_group"]]

[node name="CollisionShape2D" type="CollisionShape2D" parent="Walls/Right/StaticBody2D"]
position = Vector2(0, -2)
shape = SubResource("RectangleShape2D_53oeg")

[node name="Left" type="Sprite2D" parent="Walls"]
y_sort_enabled = true
position = Vector2(-120, 0)
texture = ExtResource("2_t3y1d")

[node name="StaticBody2D" type="StaticBody2D" parent="Walls/Left" groups=["navigation_polygon_source_geometry_group"]]

[node name="CollisionShape2D" type="CollisionShape2D" parent="Walls/Left/StaticBody2D"]
position = Vector2(0, 3)
shape = SubResource("RectangleShape2D_t3y1d")

[node name="Up" type="Sprite2D" parent="Walls"]
y_sort_enabled = true
position = Vector2(0, -132)
texture = ExtResource("3_b2abj")
offset = Vector2(0, 12)

[node name="StaticBody2D" type="StaticBody2D" parent="Walls/Up" groups=["navigation_polygon_source_geometry_group"]]

[node name="CollisionShape2D" type="CollisionShape2D" parent="Walls/Up/StaticBody2D"]
position = Vector2(0, 14.5)
shape = SubResource("RectangleShape2D_b2abj")

[node name="Down" type="Sprite2D" parent="Walls"]
y_sort_enabled = true
position = Vector2(0, 120)
texture = ExtResource("4_2turf")

[node name="StaticBody2D" type="StaticBody2D" parent="Walls/Down" groups=["navigation_polygon_source_geometry_group"]]

[node name="CollisionShape2D" type="CollisionShape2D" parent="Walls/Down/StaticBody2D"]
position = Vector2(0, 4)
shape = SubResource("RectangleShape2D_2turf")

[node name="BoardToSpawnBoss" type="Node2D" parent="."]
y_sort_enabled = true

[node name="Bird" type="Sprite2D" parent="BoardToSpawnBoss"]
visible = false
y_sort_enabled = true
position = Vector2(0, -19)
rotation = 3.14159
scale = Vector2(0.6, 0.6)
texture = ExtResource("4_icdtx")
offset = Vector2(0, -48)
hframes = 6

[node name="AnimationPlayerBird" type="AnimationPlayer" parent="BoardToSpawnBoss"]
root_node = NodePath("../Board")
libraries = {
&"": SubResource("AnimationLibrary_0mfy5")
}
speed_scale = 0.5

[node name="Board" type="Sprite2D" parent="BoardToSpawnBoss"]
y_sort_enabled = true
texture = ExtResource("2_ajitm")

[node name="Candle1" type="Sprite2D" parent="BoardToSpawnBoss/Board"]
y_sort_enabled = true
position = Vector2(-16, 0)
texture = ExtResource("3_jmbks")
hframes = 4

[node name="Candle2" type="Sprite2D" parent="BoardToSpawnBoss/Board"]
y_sort_enabled = true
position = Vector2(16, 0)
texture = ExtResource("3_jmbks")
hframes = 4

[node name="AnimationPlayerCandles" type="AnimationPlayer" parent="BoardToSpawnBoss/Board"]
libraries = {
&"": SubResource("AnimationLibrary_icdtx")
}

[node name="StaticBody2D" type="StaticBody2D" parent="BoardToSpawnBoss/Board"]

[node name="CollisionPolygon2D" type="CollisionPolygon2D" parent="BoardToSpawnBoss/Board/StaticBody2D"]
polygon = PackedVector2Array(-24, 16, -8, 16, 24, 16, 24, 3, -24, 3)

[node name="PlayerDetector" type="Area2D" parent="BoardToSpawnBoss"]
collision_layer = 0
collision_mask = 4

[node name="CollisionShape2D" type="CollisionShape2D" parent="BoardToSpawnBoss/PlayerDetector"]
position = Vector2(0, 10)
shape = SubResource("CircleShape2D_jmbks")

[node name="AnimationPlayerPanel" type="AnimationPlayer" parent="BoardToSpawnBoss"]
libraries = {
&"": SubResource("AnimationLibrary_3snc1")
}

[node name="CanvasLayer" type="CanvasLayer" parent="BoardToSpawnBoss"]

[node name="Control" type="Control" parent="BoardToSpawnBoss/CanvasLayer"]
layout_mode = 3
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -20.0
offset_top = -20.0
offset_right = 20.0
offset_bottom = 20.0
grow_horizontal = 2
grow_vertical = 2

[node name="Node2D" type="Node2D" parent="BoardToSpawnBoss/CanvasLayer/Control"]
scale = Vector2(0.8, 0.8)

[node name="Panel" type="Sprite2D" parent="BoardToSpawnBoss/CanvasLayer/Control/Node2D"]
texture = ExtResource("9_71uix")

[node name="Label" parent="BoardToSpawnBoss/CanvasLayer/Control/Node2D/Panel" instance=ExtResource("10_3snc1")]
offset_left = -62.0
offset_top = -30.0
offset_right = 145.0
offset_bottom = 37.0
scale = Vector2(0.6, 0.6)
text = "TEXT_SPAWN_BOSS_REGION_10"

[node name="Provide" type="Sprite2D" parent="BoardToSpawnBoss/CanvasLayer/Control/Node2D"]
position = Vector2(42, 18)
scale = Vector2(0.5, 0.5)
texture = ExtResource("11_uynbj")

[node name="Label" parent="BoardToSpawnBoss/CanvasLayer/Control/Node2D/Provide" instance=ExtResource("10_3snc1")]
offset_left = -28.0
offset_top = -10.0
offset_right = -2.0
offset_bottom = 12.0
text = "5"
horizontal_alignment = 2

[node name="ResourceIcon" type="Sprite2D" parent="BoardToSpawnBoss/CanvasLayer/Control/Node2D"]
position = Vector2(47, 18)
scale = Vector2(0.75, 0.688)
texture = ExtResource("12_vxowd")

[node name="Exit" type="Sprite2D" parent="BoardToSpawnBoss/CanvasLayer/Control/Node2D"]
position = Vector2(67, -35)
scale = Vector2(0.8, 0.8)
texture = ExtResource("13_rbjyc")

[node name="ButtonProvide" type="Button" parent="BoardToSpawnBoss/CanvasLayer/Control/Node2D"]
offset_left = 26.0
offset_top = 10.0
offset_right = 58.0
offset_bottom = 26.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_71uix")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_3snc1")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_uynbj")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_vxowd")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_rbjyc")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_a3oqo")
theme_override_styles/hover = SubResource("StyleBoxEmpty_y4aaa")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_i7s8p")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_lq6fy")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_1hscm")
theme_override_styles/normal = SubResource("StyleBoxEmpty_6rggy")

[node name="ButtonExit" type="Button" parent="BoardToSpawnBoss/CanvasLayer/Control/Node2D"]
offset_left = 58.0
offset_top = -46.0
offset_right = 76.0
offset_bottom = -26.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_fo70f")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_8y4xw")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_y327f")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_sa6b4")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_a5it2")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_e5bc7")
theme_override_styles/hover = SubResource("StyleBoxEmpty_6ehpm")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_ewlc0")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_csrho")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_7rm2h")
theme_override_styles/normal = SubResource("StyleBoxEmpty_11mag")

[node name="Region10Boss" parent="." instance=ExtResource("9_t3y1d")]

[node name="BossPositions" type="Node2D" parent="."]

[node name="SpawnPosition" type="Node2D" parent="BossPositions"]
position = Vector2(0, -51)

[node name="Spot1" type="Node2D" parent="BossPositions"]
position = Vector2(-77, -82)

[node name="Spot2" type="Node2D" parent="BossPositions"]
position = Vector2(77, -82)

[node name="Spot3" type="Node2D" parent="BossPositions"]
position = Vector2(77, 22)

[node name="Spot4" type="Node2D" parent="BossPositions"]
position = Vector2(-77, 22)

[node name="Spot5" type="Node2D" parent="BossPositions"]
position = Vector2(0, -51)
