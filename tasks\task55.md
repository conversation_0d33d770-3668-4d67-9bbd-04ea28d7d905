I added MainMenu scene (tscn). Read the file.
Here is how it should work:
* When player clicks QuitButton then we should close game.
* Initially SelectSavePanel should be invisible (visible = false in ready).
* When player clicks PlayButton then we should show SelectSavePanel by playing animation 'OpenLevelSelect' from AnimationPlayer. The animation will be played and it also make panel visible. When player clicks CloseButton then we should play animation 'CloseLevelSelect' from AnimationPlayer. This will play animation and close the panel.
* We have Save1-Save4 (look in tscn), in each we have:
** NewSaveIcon and NewSaveLabel - those needs to be visible when there is no given save of game. In such case LevelLabel, TimLabel, YesIcon and TimeIcon should not be visible.
** When in given Save1-4, player clicks a button SelectButton i want to either load or start new game.

You need to implement this main menu scene.

Additionally we would need to make changes to current save system. We need some place where we will have static int - in which we will store save number that was selected when loading game. Probably in ResourceManager which is an autoload - it needs to change it's game save (reload) when we select a different game save. Initially let's have it as 0 - when we select game save - it should first reload data and then we should start the game (world.tscn). Make here any required changes.