[gd_scene load_steps=12 format=3 uid="uid://bh53k1ubuweoq"]

[ext_resource type="Texture2D" uid="uid://bb0x815wcel3h" path="res://resources/solaria/buildings/tesla_coil.png" id="1_n2vep"]
[ext_resource type="Script" uid="uid://cdaorx6h8lskh" path="res://scenes/mapObjects/buildings/teslaCoil/TeslaCoil.cs" id="1_teslacoil_script"]
[ext_resource type="PackedScene" uid="uid://otpfc634hhga" path="res://scenes/UI/progress/ProgressBar.tscn" id="2_m1onc"]
[ext_resource type="PackedScene" uid="uid://bxkpne5xwk63x" path="res://scenes/mapObjects/buildings/teslaCoil/TeslaCoilBullet.tscn" id="3_teslacoilbullet"]
[ext_resource type="PackedScene" uid="uid://b6gerw1frgtmt" path="res://scenes/mapObjects/buildings/teslaCoil/TeslaCoilMenu.tscn" id="4_teslacoilmenu"]

[sub_resource type="Animation" id="Animation_reset"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite2D:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Color(1, 1, 1, 1)]
}

[sub_resource type="Animation" id="Animation_hit"]
resource_name = "hit"
length = 0.2
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite2D:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Color(1, 1, 1, 1), Color(1, 0.42, 0.27, 1), Color(1, 1, 1, 1)]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_1"]
_data = {
&"RESET": SubResource("Animation_reset"),
&"hit": SubResource("Animation_hit")
}

[sub_resource type="RectangleShape2D" id="RectangleShape2D_1"]
size = Vector2(8, 7)

[sub_resource type="RectangleShape2D" id="RectangleShape2D_n2vep"]
size = Vector2(11, 27)

[sub_resource type="CircleShape2D" id="CircleShape2D_n2vep"]
radius = 62.0322

[node name="TeslaCoil" type="Node2D"]
script = ExtResource("1_teslacoil_script")
TeslaCoilBulletScene = ExtResource("3_teslacoilbullet")

[node name="Sprite2D" type="Sprite2D" parent="."]
texture = ExtResource("1_n2vep")

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_1")
}

[node name="StaticBody2D" type="StaticBody2D" parent="."]
collision_mask = 0

[node name="CollisionShape2D" type="CollisionShape2D" parent="StaticBody2D"]
position = Vector2(0, 11.5)
shape = SubResource("RectangleShape2D_1")

[node name="ProgressBar" parent="." instance=ExtResource("2_m1onc")]
show_behind_parent = true
position = Vector2(0, 17.32)
scale = Vector2(1, 0.630031)

[node name="PlayerDetector" type="Area2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="PlayerDetector"]
position = Vector2(-0.5, 1.5)
shape = SubResource("RectangleShape2D_n2vep")

[node name="EnemyDetector" type="Area2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="EnemyDetector"]
shape = SubResource("CircleShape2D_n2vep")

[node name="TeslaCoilMenu" parent="." instance=ExtResource("4_teslacoilmenu")]
