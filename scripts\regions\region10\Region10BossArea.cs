using Godot;

public partial class Region10BossArea : Node2D
{
	private Area2D _playerDetector;
	private Node2D _canvasLayerControl;
	private Button _spawnBossButton;
	private Button _cancelButton;
	private Label _descriptionLabel;
	private bool _playerInRange = false;
	private bool _bossSpawned = false;
	private Region10Boss _currentBoss;
	private Sprite2D _walls;
	private CollisionShape2D[] _wallCollisionShapes;

	// Animation players
	private AnimationPlayer _mainAnimationPlayer;
	private AnimationPlayer _panelAnimationPlayer;
	private AnimationPlayer _candlesAnimationPlayer;
	private AnimationPlayer _birdAnimationPlayer;

	// Board references
	private Node2D _boardToSpawnBoss;
	private Sprite2D _board;

	// Region unlock handling
	private bool _isRegionUnlocked = false;
	
	public override void _Ready()
	{
		// Check if boss is already defeated
		if (GameSaveData.Instance.IsRegion10BossDefeated)
		{
			GD.Print("Region10BossArea: Boss already defeated, removing boss area");
			QueueFree();
			return;
		}

		// Check region unlock status
		CheckRegionUnlockStatus();

		// Get node references
		InitializeNodeReferences();

		// Set initial state
		InitializeInitialState();

		// Connect signals
		ConnectSignals();
		// Connect to region unlock signal for dynamic unlocking
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.RegionUnlocked += OnRegionUnlocked;
		}

		// If region is already unlocked, initialize immediately
		if (_isRegionUnlocked)
		{
			InitializeBossArea();
		}
	}

	private void CheckRegionUnlockStatus()
	{
		_isRegionUnlocked = GameSaveData.Instance.UnlockedRegions?.Contains(10) ?? false;
		GD.Print($"Region10BossArea: Region 10 unlock status: {_isRegionUnlocked}");
	}

	private void InitializeNodeReferences()
	{
		_boardToSpawnBoss = GetNode<Node2D>("BoardToSpawnBoss");
		_board = GetNode<Sprite2D>("BoardToSpawnBoss/Board");
		_playerDetector = GetNode<Area2D>("BoardToSpawnBoss/PlayerDetector");
		_canvasLayerControl = GetNode<Node2D>("BoardToSpawnBoss/CanvasLayer/Control/Node2D");
		_spawnBossButton = GetNode<Button>("BoardToSpawnBoss/CanvasLayer/Control/Node2D/ButtonProvide");
		_cancelButton = GetNode<Button>("BoardToSpawnBoss/CanvasLayer/Control/Node2D/ButtonExit");
		_descriptionLabel = GetNode<Label>("BoardToSpawnBoss/CanvasLayer/Control/Node2D/Panel/Label");

		// Animation players
		_mainAnimationPlayer = GetNode<AnimationPlayer>("AnimationPlayer");
		_panelAnimationPlayer = GetNode<AnimationPlayer>("BoardToSpawnBoss/AnimationPlayerPanel");
		_candlesAnimationPlayer = GetNode<AnimationPlayer>("BoardToSpawnBoss/Board/AnimationPlayerCandles");
		_birdAnimationPlayer = GetNode<AnimationPlayer>("BoardToSpawnBoss/AnimationPlayerBird");

		// Walls
		_walls = GetNode<Sprite2D>("Walls");
		_wallCollisionShapes = new CollisionShape2D[]
		{
			GetNode<CollisionShape2D>("Walls/Right/StaticBody2D/CollisionShape2D"),
			GetNode<CollisionShape2D>("Walls/Left/StaticBody2D/CollisionShape2D"),
			GetNode<CollisionShape2D>("Walls/Up/StaticBody2D/CollisionShape2D"),
			GetNode<CollisionShape2D>("Walls/Down/StaticBody2D/CollisionShape2D")
		};
	}

	private void InitializeInitialState()
	{
		// Set initial visibility
		_canvasLayerControl.Visible = false;
		_walls.Visible = false;

		// Disable wall collision shapes
		foreach (var collisionShape in _wallCollisionShapes)
		{
			collisionShape.Disabled = true;
		}

		// Initialize boss as hidden and disabled
		var boss = GetNode<Region10Boss>("Region10Boss");
		boss.Visible = false;
		boss.SetPhysicsProcess(false);
		boss.SetProcess(false);

		// Set description text
		_descriptionLabel.Text = Tr("TEXT_SPAWN_BOSS_REGION_10");
	}

	private void ConnectSignals()
	{
		_playerDetector.AreaEntered += OnPlayerEntered;
		_playerDetector.AreaExited += OnPlayerExited;
		_spawnBossButton.Pressed += OnSpawnBossPressed;
		_cancelButton.Pressed += OnCancelPressed;
	}

	private void InitializeBossArea()
	{
		// Start candle and bird animations
		_candlesAnimationPlayer.Play("Candles");
		_birdAnimationPlayer.Play("BirdIdle");

		GD.Print("Region10BossArea: Boss area initialized and ready for interaction");
	}
	
	public override void _ExitTree()
	{
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.RegionUnlocked -= OnRegionUnlocked;
		}
	}

	private void OnRegionUnlocked(int regionId)
	{
		if (regionId == 10 && !_isRegionUnlocked)
		{
			_isRegionUnlocked = true;
			GD.Print("Region10BossArea: Region 10 unlocked during gameplay, initializing boss area");
			InitializeBossArea();
		}
	}
	
	private void OnPlayerEntered(Area2D area)
	{
		if (area.GetParent() is PlayerController && _isRegionUnlocked)
		{
			_playerInRange = true;
			CommonSignals.Instance?.EmitShowKeyEPrompt();
			GD.Print("Region10BossArea: Player entered boss area");
		}
	}
	
	private void OnPlayerExited(Area2D area)
	{
		if (area.GetParent() is PlayerController && _isRegionUnlocked)
		{
			_playerInRange = false;
			CommonSignals.Instance?.EmitHideKeyEPrompt();
			if (_canvasLayerControl.Visible)
			{
				_panelAnimationPlayer.Play("Close");
			}
			GD.Print("Region10BossArea: Player exited boss area");
		}
	}
	
	private void OnSpawnBossPressed()
	{
		var resourcesManager = ResourcesManager.Instance;
		var beetrootCount = GameSaveData.Instance.PlayerResources.GetResourceQuantity(ResourceType.Beetroot);

		if (beetrootCount >= 5)
		{
			resourcesManager.RemoveResource(ResourceType.Beetroot, 5);
			StartBossFight();
		}
		else
		{
			GD.Print("Not enough beetroots! Need 5 beetroots.");
		}
	}
	
	private void OnCancelPressed()
	{
		_panelAnimationPlayer.Play("Close");
		GD.Print("Region10BossArea: Closing boss spawn panel");
	}

	public override void _Input(InputEvent @event)
	{
		if (@event.IsActionPressed("Interact") && _playerInRange && !_bossSpawned && !GameSaveData.Instance.IsRegion10BossDefeated && _isRegionUnlocked)
		{
			if (!_canvasLayerControl.Visible)
			{
				_panelAnimationPlayer.Play("Open");
				GD.Print("Region10BossArea: Opening boss spawn panel");
				GetViewport().SetInputAsHandled();
			}
		}
		else if (@event.IsActionPressed("Escape") && _canvasLayerControl.Visible)
		{
			_panelAnimationPlayer.Play("Close");
			GetViewport().SetInputAsHandled();
		}
	}

	private void EnableWalls()
	{
		// Make walls visible
		_walls.Visible = true;

		// Enable all wall collision shapes
		foreach (var collisionShape in _wallCollisionShapes)
		{
			collisionShape.Disabled = false;
		}
	}
	
	private void StartBossFight()
	{
		if (_bossSpawned || GameSaveData.Instance.IsRegion10BossDefeated)
			return;

		_bossSpawned = true;

		// Close panel
		_panelAnimationPlayer.Play("Close");

		// Shake camera
		PlayerController.ShakeCamera2S();

		// Play wall spawn animation
		_mainAnimationPlayer.Play("SpawnWalls");
		EnableWalls();
		// Play bird move animation
		_birdAnimationPlayer.Play("BirdMove");

		// Remove player detector
		_playerDetector.QueueFree();

		// Show and activate boss
		_currentBoss = GetNode<Region10Boss>("Region10Boss");

		// Assign boss position nodes
		var bossPositions = GetNode<Node2D>("BossPositions");
		_currentBoss.SpawnPosition = bossPositions.GetNode<Node2D>("SpawnPosition");
		_currentBoss.Spot1 = bossPositions.GetNode<Node2D>("Spot1");
		_currentBoss.Spot2 = bossPositions.GetNode<Node2D>("Spot2");
		_currentBoss.Spot3 = bossPositions.GetNode<Node2D>("Spot3");
		_currentBoss.Spot4 = bossPositions.GetNode<Node2D>("Spot4");
		_currentBoss.Spot5 = bossPositions.GetNode<Node2D>("Spot5");

		_currentBoss.Visible = true;
		_currentBoss.SetPhysicsProcess(true);
		_currentBoss.SetProcess(true);
		_currentBoss.ActivateBoss();
		_currentBoss.BossDefeated += OnBossDefeated;

		// Play boss appear animation
		var bossAnimationPlayer = _currentBoss.GetNode<AnimationPlayer>("AnimationPlayer");
		bossAnimationPlayer.Play("Appear");

		// Queue free board after 3 seconds
		GetTree().CreateTimer(3.0f).Timeout += () => {
			if (IsInstanceValid(_board))
			{
				_board.QueueFree();
			}
		};

		GD.Print("Region10BossArea: Boss fight started!");
	}

	private void OnBossDefeated()
	{
		GameSaveData.Instance.IsRegion10BossDefeated = true;
		ResourcesManager.Instance.SaveGame();

		_spawnBossButton.Text = "Boss Already Defeated";
		_spawnBossButton.Disabled = true;
		_walls.QueueFree();
	}
}
