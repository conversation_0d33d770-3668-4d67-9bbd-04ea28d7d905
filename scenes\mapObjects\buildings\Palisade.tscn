[gd_scene load_steps=7 format=3 uid="uid://c72jn0sno32k5"]

[ext_resource type="Script" uid="uid://c0l8sbdvsy48t" path="res://scenes/mapObjects/buildings/Palisade.cs" id="1_script"]
[ext_resource type="Texture2D" uid="uid://bsih3neiefwnc" path="res://resources/pixel mood/palisades/palisadeUpRight.png" id="2_74tfu"]
[ext_resource type="PackedScene" uid="uid://otpfc634hhga" path="res://scenes/UI/progress/ProgressBar.tscn" id="3_progressbar"]

[sub_resource type="Animation" id="Animation_reset"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite2D:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Color(1, 1, 1, 1)]
}

[sub_resource type="Animation" id="Animation_hit"]
resource_name = "hit"
length = 0.2
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite2D:modulate")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Color(1, 1, 1, 1), Color(1, 0.42, 0.27, 1), Color(1, 1, 1, 1)]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_1"]
_data = {
&"RESET": SubResource("Animation_reset"),
&"hit": SubResource("Animation_hit")
}

[node name="Palisade" type="Node2D"]
script = ExtResource("1_script")

[node name="Sprite2D" type="Sprite2D" parent="."]
texture = ExtResource("2_74tfu")

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_1")
}

[node name="StaticBody2D" type="StaticBody2D" parent="."]
collision_mask = 0

[node name="DownLeft" type="CollisionPolygon2D" parent="StaticBody2D"]
visible = false
polygon = PackedVector2Array(-8, 0, 4, 0, 4, 16, -3, 16, -3, 14, -8, 12)

[node name="DownRight" type="CollisionPolygon2D" parent="StaticBody2D"]
visible = false
polygon = PackedVector2Array(8, 0, 8, 13, 7, 14, 4, 14, 4, 16, -3, 16, -3, 0)

[node name="LeftRight" type="CollisionPolygon2D" parent="StaticBody2D"]
visible = false
polygon = PackedVector2Array(-8, 0, -8, 14, 8, 14, 8, 0)

[node name="LeftRightDown" type="CollisionPolygon2D" parent="StaticBody2D"]
visible = false
polygon = PackedVector2Array(8, 0, 8, 14, 4, 14, 4, 16, -3, 16, -3, 14, -8, 14, -8, 0)

[node name="LeftRightTop" type="CollisionPolygon2D" parent="StaticBody2D"]
visible = false
polygon = PackedVector2Array(-8, 14, 8, 14, 8, 0, 3, 0, 3, -16, -2, -16, -2, 0, -8, 0)

[node name="LeftRightTopDown" type="CollisionPolygon2D" parent="StaticBody2D"]
visible = false
polygon = PackedVector2Array(-8, 14, -3, 14, -3, 16, 4, 16, 4, 14, 8, 14, 8, 0, 4, 0, 4, -16, -3, -16, -3, 0, -8, 0)

[node name="LeftTopDown" type="CollisionPolygon2D" parent="StaticBody2D"]
visible = false
polygon = PackedVector2Array(4, 16, -3, 16, -3, 14, -8, 14, -8, 0, -3, 0, -3, -16, 4, -16)

[node name="RightTopDown" type="CollisionPolygon2D" parent="StaticBody2D"]
visible = false
polygon = PackedVector2Array(4, 16, -3, 16, -3, -16, 4, -16, 4, 0, 8, 0, 8, 12)

[node name="TopDown" type="CollisionPolygon2D" parent="StaticBody2D"]
visible = false
polygon = PackedVector2Array(-3, -16, 4, -16, 4, 16, -3, 16)

[node name="UpLeft" type="CollisionPolygon2D" parent="StaticBody2D"]
visible = false
polygon = PackedVector2Array(4, -16, -3, -16, -3, 0, -8, 0, -8, 14, 2, 14, 4, 11)

[node name="UpRight" type="CollisionPolygon2D" parent="StaticBody2D"]
visible = false
polygon = PackedVector2Array(8, 14, -1, 14, -3, 12, -3, -16, 3, -16, 3, 0, 8, 0)

[node name="ProgressBar" parent="." instance=ExtResource("3_progressbar")]
show_behind_parent = true
