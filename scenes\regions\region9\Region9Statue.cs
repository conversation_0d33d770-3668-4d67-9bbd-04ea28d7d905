using Godot;

public partial class Region9Statue : Node2D
{
	private Area2D _playerDetector;
	private AnimationPlayer _animationPlayer;
	private Button _closeButton;
	private Label _header;
	private Label _description;
	private bool _isPlayerInRange = false;
	private bool _isMenuOpen = false;

	public override void _Ready()
	{
		_playerDetector = GetNode<Area2D>("PlayerDetector");
		_animationPlayer = GetNode<AnimationPlayer>("AnimationPlayer");
		_closeButton = GetNode<Button>("CanvasLayer/Control/Panel/CloseButton");
		_header = GetNode<Label>("CanvasLayer/Control/Panel/Header");
		_description = GetNode<Label>("CanvasLayer/Control/Panel/Description");
		GetNode<Node2D>("CanvasLayer/Control/Panel").Visible = false;
		
		if (_playerDetector != null)
		{
			_playerDetector.AreaEntered += OnPlayerEntered;
			_playerDetector.AreaExited += OnPlayerExited;
		}

		if (_closeButton != null)
		{
			_closeButton.Pressed += OnCloseButtonPressed;
		}

		SetupStatueText();
	}

	public override void _ExitTree()
	{
		if (_playerDetector != null)
		{
			_playerDetector.AreaEntered -= OnPlayerEntered;
			_playerDetector.AreaExited -= OnPlayerExited;
		}

		if (_closeButton != null)
		{
			_closeButton.Pressed -= OnCloseButtonPressed;
		}
	}

	public override void _Input(InputEvent @event)
	{
		if (!_isPlayerInRange) return;

		if (@event.IsActionPressed("Interact"))
		{
			if (!_isMenuOpen)
			{
				OpenMenu();
			}
		}
	}

	private void OnPlayerEntered(Area2D area)
	{
		if (area.Name == "PlayerDetector")
		{
			_isPlayerInRange = true;
			CommonSignals.Instance?.EmitShowKeyEPrompt();
		}
	}

	private void OnPlayerExited(Area2D area)
	{
		if (area.Name == "PlayerDetector")
		{
			_isPlayerInRange = false;
			CommonSignals.Instance?.EmitHideKeyEPrompt();
		}
	}

	private void OpenMenu()
	{
		if (_animationPlayer != null)
		{
			_isMenuOpen = true;
			_animationPlayer.Play("Open");
			UnlockStatueBuff();
		}
	}

	private void OnCloseButtonPressed()
	{
		CloseMenu();
	}

	private void CloseMenu()
	{
		if (_animationPlayer != null)
		{
			_isMenuOpen = false;
			_animationPlayer.Play("Close");
		}
	}

	private void UnlockStatueBuff()
	{
		var statueBuffs = GameSaveData.Instance.StatueBuffs;
		if (statueBuffs != null)
		{
			var statueBuff1 = statueBuffs.Find(buff => buff.Id == "StatueBuff1");
			if (statueBuff1 != null && !statueBuff1.IsUnlocked)
			{
				statueBuff1.IsUnlocked = true;
				GD.Print("Region9Statue: StatueBuff1 unlocked!");
			}
		}
	}

	private void SetupStatueText()
	{
		if (_header != null)
		{
			_header.Text = Tr("STATUE_HEADER");
		}

		if (_description != null)
		{
			_description.Text = Tr("STATUE_DESCRIPTION");
		}
	}
}
