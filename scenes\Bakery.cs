using Godot;

public partial class Bakery : Node2D
{
	[Export] public float BakeIntervalSeconds { get; set; } = 5.0f;
	
	private Timer _bakeTimer;
	private static Bakery _instance;
	
	public override void _Ready()
	{
		_instance = this;
		
		_bakeTimer = new Timer();
		_bakeTimer.WaitTime = BakeIntervalSeconds;
		_bakeTimer.Timeout += OnBakeTimer;
		_bakeTimer.Autostart = true;
		AddChild(_bakeTimer);
		
		GD.Print($"Bakery: Started with bake interval of {BakeIntervalSeconds} seconds");
	}
	
	public override void _ExitTree()
	{
		if (_bakeTimer != null)
		{
			_bakeTimer.Timeout -= OnBakeTimer;
		}
		
		if (_instance == this)
		{
			_instance = null;
		}
	}
	
	private void OnBakeTimer()
	{
		BakeNavigationRegion();
	}
	
	private void BakeNavigationRegion()
	{
		var navigationRegion = GetNodeOrNull<NavigationRegion2D>("/root/world/NavigationRegion2D");
		if (navigationRegion != null)
		{
			if (!navigationRegion.IsBaking())
			{
				navigationRegion.BakeNavigationPolygon(true);
				GD.Print("Bakery: Navigation region baked");
			}
			else
			{
				GD.Print("Bakery: Navigation region is already baking, skipping");
			}
		}
		else
		{
			// Don't print error - this is normal when not in world scene
			GD.Print("Bakery: NavigationRegion2D not found (not in world scene)");
		}
	}
	
	public static void Bake()
	{
		if (_instance != null)
		{
			_instance.BakeNavigationRegion();
		}
		else
		{
			var navigationRegion = GetNodeStatic<NavigationRegion2D>("/root/world/NavigationRegion2D");
			if (navigationRegion != null)
			{
				if (!navigationRegion.IsBaking())
				{
					navigationRegion.BakeNavigationPolygon(true);
					GD.Print("Bakery.Bake(): Navigation region baked (static call)");
				}
				else
				{
					GD.Print("Bakery.Bake(): Navigation region is already baking, skipping (static call)");
				}
			}
			else
			{
				// Don't print error - this is normal when not in world scene
				GD.Print("Bakery.Bake(): NavigationRegion2D not found (not in world scene)");
			}
		}
	}
	
	private static T GetNodeStatic<T>(string path) where T : Node
	{
		var tree = Engine.GetMainLoop() as SceneTree;
		return tree?.CurrentScene?.GetNodeOrNull<T>(path);
	}
}
