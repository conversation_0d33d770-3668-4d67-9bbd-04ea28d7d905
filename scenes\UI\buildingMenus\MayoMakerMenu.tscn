[gd_scene load_steps=50 format=3 uid="uid://bcm3geeq3yv01"]

[ext_resource type="Script" uid="uid://b58o2n5als4ao" path="res://scenes/UI/buildingMenus/MayoMakerMenu.cs" id="1_mayo_menu"]
[ext_resource type="Texture2D" uid="uid://cyvhsyv6xia6m" path="res://resources/solaria/UI/build/build_panel.png" id="2_mayo_menu"]
[ext_resource type="Texture2D" uid="uid://bgpd0wfpx3kvj" path="res://resources/solaria/UI/inventory/inventory_item_single_slot.png" id="3_mayo_menu"]
[ext_resource type="Texture2D" uid="uid://3svkh64jk5jw" path="res://resources/solaria/resources/mayonnaise.png" id="4_mayo_menu"]
[ext_resource type="PackedScene" uid="uid://brynlg0mgkf76" path="res://scenes/UI/common/Label.tscn" id="6_mayo_menu"]
[ext_resource type="Texture2D" path="res://resources/solaria/resources/egg.png" id="7_mayo_menu"]
[ext_resource type="Texture2D" uid="uid://b5wugfo5k4t1g" path="res://resources/solaria/UI/build/buildButton2.png" id="8_mayo_menu"]
[ext_resource type="Texture2D" uid="uid://clpqdxl0rnm1e" path="res://resources/solaria/UI/build/build_panel_select_amount.png" id="9_mayo_menu"]
[ext_resource type="Texture2D" uid="uid://dl8bp5u75xxvx" path="res://resources/solaria/UI/build/craft_amount.png" id="10_mayo_menu"]
[ext_resource type="Texture2D" uid="uid://cdxye6tum1anb" path="res://resources/solaria/UI/inventory/close_button.png" id="11_mayo_menu"]
[ext_resource type="Texture2D" uid="uid://b4qihgrhky4rk" path="res://resources/solaria/UI/build/button1.png" id="12_mayo_menu"]
[ext_resource type="Texture2D" uid="uid://dvv01dmhem1hh" path="res://resources/solaria/UI/build/button2.png" id="13_mayo_menu"]

[sub_resource type="Animation" id="Animation_mayo_close"]
resource_name = "Close"
length = 0.09
step = 0.01
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Control/Panel:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.03, 0.09),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1.05, 1.05), Vector2(0.95, 0.95)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Control/Panel:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0.09),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}

[sub_resource type="Animation" id="Animation_mayo_open"]
resource_name = "Open"
length = 0.09
step = 0.01
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Control/Panel:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.06, 0.09),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(0.95, 0.95), Vector2(1.05, 1.05), Vector2(1, 1)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Control/Panel:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}

[sub_resource type="Animation" id="Animation_mayo_reset"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Control/Panel:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(0.95, 0.95)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Control/Panel:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_mayo"]
_data = {
&"Close": SubResource("Animation_mayo_close"),
&"Open": SubResource("Animation_mayo_open"),
&"RESET": SubResource("Animation_mayo_reset")
}

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_1"]
[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_1"]
[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_2"]
[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_3"]
[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_4"]
[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_5"]
[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_6"]
[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_7"]
[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_8"]
[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_9"]
[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_10"]
[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_11"]
[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_12"]
[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_13"]
[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_14"]
[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_15"]
[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_16"]
[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_17"]
[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_18"]
[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_19"]
[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_20"]
[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_21"]
[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_22"]
[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_23"]
[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_24"]
[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_25"]
[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_26"]
[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_27"]
[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_28"]
[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_29"]
[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_30"]

[node name="MayoMakerMenu" type="CanvasLayer"]
script = ExtResource("1_mayo_menu")

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_mayo")
}

[node name="Control" type="Control" parent="."]
layout_mode = 3
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -20.0
offset_top = -20.0
offset_right = 20.0
offset_bottom = 20.0
grow_horizontal = 2
grow_vertical = 2

[node name="Panel" type="Sprite2D" parent="Control"]
visible = false
scale = Vector2(0.95, 0.95)
texture = ExtResource("2_mayo_menu")

[node name="ScrollContainer" type="ScrollContainer" parent="Control/Panel"]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -92.0
offset_top = -116.0
offset_right = -99.0
offset_bottom = -124.0
grow_horizontal = 2
grow_vertical = 2

[node name="VBoxContainer" type="VBoxContainer" parent="Control/Panel/ScrollContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3

[node name="ItemListMayo" type="ItemList" parent="Control/Panel/ScrollContainer/VBoxContainer"]
custom_minimum_size = Vector2(0, 40)
layout_mode = 2
theme_override_styles/focus = SubResource("StyleBoxEmpty_1")
theme_override_styles/panel = SubResource("StyleBoxFlat_1")
theme_override_styles/hovered = SubResource("StyleBoxEmpty_2")
theme_override_styles/hovered_selected = SubResource("StyleBoxEmpty_3")
theme_override_styles/hovered_selected_focus = SubResource("StyleBoxEmpty_4")
theme_override_styles/selected = SubResource("StyleBoxEmpty_5")
theme_override_styles/selected_focus = SubResource("StyleBoxEmpty_6")
theme_override_styles/cursor_unfocused = SubResource("StyleBoxEmpty_7")
theme_override_styles/cursor = SubResource("StyleBoxEmpty_8")

[node name="IconBackground" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListMayo"]
position = Vector2(17.4737, 19.3684)
texture = ExtResource("3_mayo_menu")

[node name="IconForeground" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListMayo"]
position = Vector2(17.4737, 19.3684)
texture = ExtResource("4_mayo_menu")

[node name="BuildButton" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListMayo"]
position = Vector2(165.684, 21.2632)
scale = Vector2(1.49, 1.49)
texture = ExtResource("8_mayo_menu")

[node name="ItemName" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListMayo" instance=ExtResource("6_mayo_menu")]
layout_mode = 0
offset_left = 34.0
offset_top = -1.0
offset_right = 181.0
offset_bottom = 17.0
scale = Vector2(0.73, 0.73)
text = "MAYO_TEXT"
horizontal_alignment = 0

[node name="ItemDescription" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListMayo" instance=ExtResource("6_mayo_menu")]
layout_mode = 0
offset_left = 34.0
offset_top = 12.0
offset_right = 219.0
offset_bottom = 69.0
scale = Vector2(0.495, 0.495)
text = "MAYO_DESCRIPTION"
horizontal_alignment = 0
vertical_alignment = 0

[node name="Button" type="Button" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListMayo"]
layout_mode = 0
offset_left = 150.0
offset_top = 4.0
offset_right = 181.0
offset_bottom = 36.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_9")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_10")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_11")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_12")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_13")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_14")
theme_override_styles/hover = SubResource("StyleBoxEmpty_15")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_16")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_17")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_18")
theme_override_styles/normal = SubResource("StyleBoxEmpty_19")

[node name="PriceIcon2" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListMayo"]
position = Vector2(141.474, 16)
texture = ExtResource("7_mayo_menu")

[node name="Price2" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListMayo" instance=ExtResource("6_mayo_menu")]
layout_mode = 0
offset_left = 127.789
offset_top = 11.7895
offset_right = 211.789
offset_bottom = 28.7895
scale = Vector2(0.73, 0.73)
text = "2"
horizontal_alignment = 0

[node name="InfoBoard" type="Sprite2D" parent="Control/Panel"]
position = Vector2(158.948, -45.249)
scale = Vector2(1.05263, 1.05263)
texture = ExtResource("9_mayo_menu")

[node name="ItemBackground" type="Sprite2D" parent="Control/Panel/InfoBoard"]
position = Vector2(0.999268, -51.0135)
texture = ExtResource("3_mayo_menu")

[node name="InfoLabel" parent="Control/Panel/InfoBoard" instance=ExtResource("6_mayo_menu")]
offset_left = -52.0006
offset_top = -28.0135
offset_right = 91.9994
offset_bottom = -12.0135
scale = Vector2(0.73, 0.73)
text = "SELECT_AMOUNT"

[node name="ItemFront" type="Sprite2D" parent="Control/Panel/InfoBoard"]
position = Vector2(0.999268, -51.0135)

[node name="SelectAmountBg" type="Sprite2D" parent="Control/Panel/InfoBoard"]
position = Vector2(-1.00085, 0.986572)
scale = Vector2(1.5, 1.5)
texture = ExtResource("10_mayo_menu")

[node name="AmountToProduce" parent="Control/Panel/InfoBoard" instance=ExtResource("6_mayo_menu")]
offset_left = -15.0006
offset_top = -13.0135
offset_right = 22.9994
offset_bottom = 20.9865
scale = Vector2(0.73, 0.73)
text = "1"

[node name="Amount1" type="Sprite2D" parent="Control/Panel/InfoBoard"]
position = Vector2(-37.0006, 35.9866)
scale = Vector2(0.5, 0.5)
texture = ExtResource("12_mayo_menu")

[node name="AmountToProduce" parent="Control/Panel/InfoBoard/Amount1" instance=ExtResource("6_mayo_menu")]
offset_left = -14.0
offset_top = -14.3334
offset_right = 24.0
offset_bottom = 19.6666
scale = Vector2(0.73, 0.73)
text = "x1"

[node name="Amount25" type="Sprite2D" parent="Control/Panel/InfoBoard"]
position = Vector2(-12.0004, 35.9865)
scale = Vector2(0.5, 0.5)
texture = ExtResource("12_mayo_menu")

[node name="AmountToProduce" parent="Control/Panel/InfoBoard/Amount25" instance=ExtResource("6_mayo_menu")]
offset_left = -14.0
offset_top = -14.3334
offset_right = 24.0
offset_bottom = 19.6666
scale = Vector2(0.73, 0.73)
text = "25%"

[node name="Amount50" type="Sprite2D" parent="Control/Panel/InfoBoard"]
position = Vector2(12.9996, 35.9865)
scale = Vector2(0.5, 0.5)
texture = ExtResource("12_mayo_menu")

[node name="AmountToProduce" parent="Control/Panel/InfoBoard/Amount50" instance=ExtResource("6_mayo_menu")]
offset_left = -14.0
offset_top = -14.3334
offset_right = 24.0
offset_bottom = 19.6666
scale = Vector2(0.73, 0.73)
text = "50%"

[node name="AmountMax" type="Sprite2D" parent="Control/Panel/InfoBoard"]
position = Vector2(37.9996, 35.9865)
scale = Vector2(0.5, 0.5)
texture = ExtResource("12_mayo_menu")

[node name="AmountToProduce" parent="Control/Panel/InfoBoard/AmountMax" instance=ExtResource("6_mayo_menu")]
offset_left = -14.0
offset_top = -14.3334
offset_right = 24.0
offset_bottom = 19.6666
scale = Vector2(0.73, 0.73)
text = "MAX"

[node name="ProduceButton" type="Sprite2D" parent="Control/Panel/InfoBoard"]
position = Vector2(-1.00061, 56.9865)
scale = Vector2(0.59, 0.59)
texture = ExtResource("13_mayo_menu")

[node name="ProduceLabel" parent="Control/Panel/InfoBoard/ProduceButton" instance=ExtResource("6_mayo_menu")]
anchors_preset = -1
anchor_left = -0.0233037
anchor_top = 0.00794512
anchor_right = 0.757946
anchor_bottom = 0.257945
offset_left = -30.5086
offset_top = -15.2542
offset_right = 7.49143
offset_bottom = 18.7458
scale = Vector2(0.73, 0.73)
text = "PRODUCE_TEXT"
metadata/_edit_use_anchors_ = true

[node name="ButtonMinusOne" type="Button" parent="Control/Panel/InfoBoard"]
offset_left = -21.0006
offset_top = 8.98651
offset_right = -7.00061
offset_bottom = 22.9865
theme_override_styles/focus = SubResource("StyleBoxEmpty_20")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_21")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_22")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_23")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_24")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_25")
theme_override_styles/hover = SubResource("StyleBoxEmpty_26")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_27")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_28")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_29")
theme_override_styles/normal = SubResource("StyleBoxEmpty_30")

[node name="ButtonPlusOne" type="Button" parent="Control/Panel/InfoBoard"]
offset_left = 4.99915
offset_top = 8.98657
offset_right = 18.9991
offset_bottom = 22.9866
theme_override_styles/focus = SubResource("StyleBoxEmpty_20")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_21")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_22")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_23")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_24")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_25")
theme_override_styles/hover = SubResource("StyleBoxEmpty_26")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_27")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_28")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_29")
theme_override_styles/normal = SubResource("StyleBoxEmpty_30")

[node name="ButtonSetOne" type="Button" parent="Control/Panel/InfoBoard"]
offset_left = -49.0
offset_top = 28.0
offset_right = -25.0
offset_bottom = 44.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_20")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_21")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_22")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_23")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_24")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_25")
theme_override_styles/hover = SubResource("StyleBoxEmpty_26")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_27")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_28")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_29")
theme_override_styles/normal = SubResource("StyleBoxEmpty_30")

[node name="ButtonSet25Percent" type="Button" parent="Control/Panel/InfoBoard"]
offset_left = -24.0
offset_top = 28.0
offset_bottom = 44.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_20")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_21")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_22")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_23")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_24")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_25")
theme_override_styles/hover = SubResource("StyleBoxEmpty_26")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_27")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_28")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_29")
theme_override_styles/normal = SubResource("StyleBoxEmpty_30")

[node name="ButtonSet50Percent" type="Button" parent="Control/Panel/InfoBoard"]
offset_left = 1.0
offset_top = 28.0
offset_right = 25.0
offset_bottom = 44.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_20")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_21")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_22")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_23")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_24")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_25")
theme_override_styles/hover = SubResource("StyleBoxEmpty_26")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_27")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_28")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_29")
theme_override_styles/normal = SubResource("StyleBoxEmpty_30")

[node name="ButtonSetMax" type="Button" parent="Control/Panel/InfoBoard"]
offset_left = 26.0
offset_top = 28.0
offset_right = 50.0
offset_bottom = 44.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_20")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_21")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_22")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_23")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_24")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_25")
theme_override_styles/hover = SubResource("StyleBoxEmpty_26")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_27")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_28")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_29")
theme_override_styles/normal = SubResource("StyleBoxEmpty_30")

[node name="ButtonProduce" type="Button" parent="Control/Panel/InfoBoard"]
offset_left = -20.0
offset_top = 47.0
offset_right = 18.0
offset_bottom = 66.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_20")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_21")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_22")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_23")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_24")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_25")
theme_override_styles/hover = SubResource("StyleBoxEmpty_26")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_27")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_28")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_29")
theme_override_styles/normal = SubResource("StyleBoxEmpty_30")

[node name="Close" type="Sprite2D" parent="Control/Panel"]
position = Vector2(218.105, -119.789)
texture = ExtResource("11_mayo_menu")

[node name="CloseButton" type="Button" parent="Control/Panel"]
offset_left = 207.948
offset_top = -132.21
offset_right = 227.947
offset_bottom = -110.21
theme_override_styles/focus = SubResource("StyleBoxEmpty_9")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_10")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_11")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_12")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_13")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_14")
theme_override_styles/hover = SubResource("StyleBoxEmpty_15")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_16")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_17")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_18")
theme_override_styles/normal = SubResource("StyleBoxEmpty_19")
