TASK-1:
I want to add a completly new building called Palisade. It can be build just like other buildings. It takes 1 tile width and 2 tiles height. This building will not produce anything, it will just have some amount of HP, let's say 15 (i will adjust later if needed). When player hits it with pickaxe or a sword or arrow then it should be damaged (just like other buildings or enemies or rabbit). I have images of palisades in res://resources/pixel mood/palisades/ folder. Basic palisade that will be used in building menu should be palisadeLeftRight.png. Placing palisade requires 5 WoodenBeam. When we save/load game - we want to preserve this palisade just like other buildings. Now, we have palisades in differnt directions (different texures). But it's the same building, just textures needs to be adjusted based on which direction it is facing. Default is palisadeLeftRight.png. It is just a palisade nonnecting palisade from left side and right side. We have also:
* palisadeTopDown.png - when we have palisade connecting top to bottom
* palisadeDownLeft.png - when we have palisade connecting down to left
* palisadeDownRight.png - when we have palisade connecting down to right
* palisadeUpLeft.png - when we have palisade connecting left to top
* palisadeUpRight.png - when we have palisade connecting right to top
When we place a palisade we need to somehow inform palisades around that there is a new palisade and they need to adjust their texture. Also the palisade that was just placed needs to set proper texture.
Look how position of for example SeedMaker is calculated. It also has 1 tile width and 2 tiles height (1 tile is 16x16px). So calculate position of palisade the same way as seed maker.
Palisade needs to also have following features, similar to seed maker:
* StaticBody2D with collision shape
* ProgressBar (which is a health bar of palisade, show it only if health is below 100%)

Player (sword, arrow) and enemies (like melee goblin - we only have melee goblin currently) should be able to attack palisade.

Make sure you add palisade to build menu as last item list in menu (duplicate other item list).
Make sure you assign palisade to building placer, building manager and in building placer apply proper logic.
Make sure you don't break any enum numerations.
Understand like system of seed maker works (i mean placing, object type etc)
Set new objecttypeplaced for palisade.
Set new objecttype from palisade.
Add translations for palisade when you add it to build menu.
Make sure everything that is required to build a building is implemented in terms of palisade.
Make sure the project builds - build it at the end.
Do your best to implement is as good as you only can.
(!)Your first step should be to determine how everything works so that you don't make any mistakes.
(!)Verify your changes at the end - check if all points from this task are implemented.