using Godot;
using System;

public partial class PlayerStatsManager : Node
{
	public static PlayerStatsManager Instance { get; private set; }

	[Export] public int MaxEnergy { get; set; } = 10;
	[Export] public int MaxFood { get; set; } = 30;
	[Export] public int MaxWater { get; set; } = 30;
	[Export] public int MaxHealth { get; set; } = 10;

	public int CurrentEnergy { get; private set; } = 10;
	public int CurrentFood { get; private set; } = 30;
	public int CurrentWater { get; private set; } = 30;
	public int CurrentHealth { get; private set; } = 10;

	private int _actionCount = 0;
	private Timer _energyRestoreTimer;
	private float _energyRestoreInterval = 1.0f;
	private float _minEnergyRestoreInterval = 0.05f;
	private bool _isEnergyRestoring = false;
	private ColorRect _dangerOverlay;

	[Signal] public delegate void EnergyChangedEventHandler(int current, int max);
	[Signal] public delegate void FoodChangedEventHandler(int current, int max);
	[Signal] public delegate void WaterChangedEventHandler(int current, int max);
	[Signal] public delegate void HealthChangedEventHandler(int current, int max);
	[Signal] public delegate void PlayerDiedEventHandler();

	public override void _Ready()
	{
		if (Instance == null)
		{
			Instance = this;
		}
		else
		{
			QueueFree();
			return;
		}

		SetupTimers();
		SetupDangerOverlay();
		ConnectSignals();
		LoadStats();

		// Ensure player starts with maximum energy on game start
		SetEnergyToMax();

		EmitSignal(SignalName.EnergyChanged, CurrentEnergy, MaxEnergy);
		EmitSignal(SignalName.FoodChanged, CurrentFood, MaxFood);
		EmitSignal(SignalName.WaterChanged, CurrentWater, MaxWater);
		EmitSignal(SignalName.HealthChanged, CurrentHealth, MaxHealth);
	}

	private void SetupTimers()
	{
		_energyRestoreTimer = new Timer();
		_energyRestoreTimer.WaitTime = 3.0f;
		_energyRestoreTimer.OneShot = true;
		_energyRestoreTimer.Timeout += StartEnergyRestore;
		AddChild(_energyRestoreTimer);
	}

	private void SetupDangerOverlay()
	{
		_dangerOverlay = new ColorRect();
		_dangerOverlay.Color = new Color(1.0f, 0.0f, 0.0f, 0.15f);
		_dangerOverlay.MouseFilter = Control.MouseFilterEnum.Ignore;
		_dangerOverlay.SetAnchorsPreset(Control.LayoutPreset.FullRect);
		_dangerOverlay.Visible = false;
		_dangerOverlay.ZIndex = 1000; // High Z-index to ensure it's on top

		var currentScene = GetTree().CurrentScene;
		if (currentScene != null)
		{
			// Try to find a UI layer first, otherwise add to scene root
			var uiLayer = currentScene.GetNodeOrNull<CanvasLayer>("UI");
			if (uiLayer != null)
			{
				uiLayer.AddChild(_dangerOverlay);
				GD.Print("PlayerStatsManager: Added danger overlay to UI layer");
			}
			else
			{
				currentScene.AddChild(_dangerOverlay);
				GD.Print("PlayerStatsManager: Added danger overlay to scene root (no UI layer found)");
			}
		}
		else
		{
			GD.PrintErr("PlayerStatsManager: Could not get current scene for danger overlay!");
		}
	}

	private void ConnectSignals()
	{
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.ToolUsed += OnToolUsed;
		}
	}

	public void OnToolUsed()
	{
		UseEnergy(1);
		_actionCount++;

		if (_actionCount % 4 == 0)
		{
			UseFood(1);
		}

		if (_actionCount % 5 == 0)
		{
			UseWater(1);
		}

		ResetEnergyRestoreTimer();
	}

	private void UseEnergy(int amount)
	{
		CurrentEnergy = Math.Max(0, CurrentEnergy - amount);
		UpdateEnergyInGameData();
		EmitSignal(SignalName.EnergyChanged, CurrentEnergy, MaxEnergy);
	}

	private void UseFood(int amount)
	{
		CurrentFood = Math.Max(0, CurrentFood - amount);
		UpdateFoodInGameData();
		EmitSignal(SignalName.FoodChanged, CurrentFood, MaxFood);
		UpdateDangerOverlay();
	}

	private void UseWater(int amount)
	{
		CurrentWater = Math.Max(0, CurrentWater - amount);
		UpdateWaterInGameData();
		EmitSignal(SignalName.WaterChanged, CurrentWater, MaxWater);
		UpdateDangerOverlay();
	}

	public void AddFood(int amount)
	{
		CurrentFood = Math.Min(MaxFood, CurrentFood + amount);
		UpdateFoodInGameData();
		EmitSignal(SignalName.FoodChanged, CurrentFood, MaxFood);
		UpdateDangerOverlay();
	}

	public void AddWater(int amount)
	{
		CurrentWater = Math.Min(MaxWater, CurrentWater + amount);
		UpdateWaterInGameData();
		EmitSignal(SignalName.WaterChanged, CurrentWater, MaxWater);
		UpdateDangerOverlay();
	}

	public void AddHealth(int amount)
	{
		CurrentHealth = Math.Min(MaxHealth, CurrentHealth + amount);
		UpdateHealthInGameData();
		EmitSignal(SignalName.HealthChanged, CurrentHealth, MaxHealth);
	}

	private void SetEnergyToMax()
	{
		CurrentEnergy = MaxEnergy;
		UpdateEnergyInGameData();
		GD.Print($"PlayerStatsManager: Set energy to max ({CurrentEnergy}/{MaxEnergy})");
	}

	private void ResetEnergyRestoreTimer()
	{
		_isEnergyRestoring = false;
		_energyRestoreTimer.Stop();
		_energyRestoreTimer.Start();
	}

	private void StartEnergyRestore()
	{
		if (CurrentEnergy < MaxEnergy)
		{
			_isEnergyRestoring = true;
			_energyRestoreInterval = 0.5f;
			RestoreEnergyStep();
		}
	}

	private void RestoreEnergyStep()
	{
		if (!_isEnergyRestoring || CurrentEnergy >= MaxEnergy)
			return;

		CurrentEnergy = Math.Min(MaxEnergy, CurrentEnergy + 1);
		UpdateEnergyInGameData();
		EmitSignal(SignalName.EnergyChanged, CurrentEnergy, MaxEnergy);

		if (CurrentEnergy < MaxEnergy)
		{
			_energyRestoreInterval = Math.Max(_minEnergyRestoreInterval, _energyRestoreInterval - 0.05f);
			GetTree().CreateTimer(_energyRestoreInterval).Timeout += RestoreEnergyStep;
		}
	}

	private void UpdateDangerOverlay()
	{
		if (_dangerOverlay == null || !IsInstanceValid(_dangerOverlay))
		{
			GD.Print("PlayerStatsManager: Danger overlay is null or disposed, recreating...");
			SetupDangerOverlay();
			if (_dangerOverlay == null || !IsInstanceValid(_dangerOverlay))
			{
				GD.Print("PlayerStatsManager: Failed to recreate danger overlay!");
				return;
			}
		}

		bool showDanger = CurrentFood == 0 || CurrentWater == 0;
		_dangerOverlay.Visible = showDanger;

		if (showDanger)
		{
			float alpha = (CurrentFood == 0 && CurrentWater == 0) ? 0.25f : 0.15f;
			_dangerOverlay.Color = new Color(1.0f, 0.0f, 0.0f, alpha);
			GD.Print($"PlayerStatsManager: Showing danger overlay - Food: {CurrentFood}, Water: {CurrentWater}, Alpha: {alpha}");
		}
		else
		{
			GD.Print("PlayerStatsManager: Hiding danger overlay");
		}
	}



	public float GetSpeedModifier()
	{
		if (CurrentFood == 0 && CurrentWater == 0)
			return 0.4f;
		else if (CurrentFood == 0 || CurrentWater == 0)
			return 0.6f;
		else
			return 1.0f;
	}

	public float GetAnimationSpeedModifier()
	{
		return GetSpeedModifier();
	}

	public void ConsumeBerry()
	{
		AddFood(5);
		AddWater(3);
		GD.Print("Consumed berry: +5 food, +3 water");
	}

	public void ConsumeRawRabbitLeg()
	{
		AddFood(4);
		GD.Print("Consumed raw rabbit leg: +4 food");
	}

	public void ConsumeCookedRabbitLeg()
	{
		AddHealth(10);
		GD.Print("Consumed cooked rabbit leg: +10 health");
	}

	public void ConsumeBrownMushroom()
	{
		AddFood(2);
		AddWater(1);
		GD.Print("Consumed brown mushroom: +2 food, +1 water");
	}

	public void ConsumeBlueMushroom()
	{
		AddWater(5);
		GD.Print("Consumed blue mushroom: +5 water");
	}

	public void ConsumeVioletMushroom()
	{
		AddHealth(3);
		AddFood(1);
		GD.Print("Consumed violet mushroom: +3 health, +1 food");
	}

	private void LoadStats()
	{
		var gameData = GameSaveData.Instance;
		CurrentHealth = (int)gameData.PlayerStats.Health;
		CurrentEnergy = (int)gameData.PlayerStats.Energy;
		CurrentFood = (int)gameData.PlayerStats.Food;
		CurrentWater = (int)gameData.PlayerStats.Water;
		GD.Print($"PlayerStatsManager: Loaded stats - Health: {CurrentHealth}, Energy: {CurrentEnergy}, Food: {CurrentFood}, Water: {CurrentWater}");
	}

	private void UpdateHealthInGameData()
	{
		GameSaveData.Instance.PlayerStats.Health = CurrentHealth;
	}

	private void UpdateEnergyInGameData()
	{
		GameSaveData.Instance.PlayerStats.Energy = CurrentEnergy;
	}

	private void UpdateFoodInGameData()
	{
		GameSaveData.Instance.PlayerStats.Food = CurrentFood;
	}

	private void UpdateWaterInGameData()
	{
		GameSaveData.Instance.PlayerStats.Water = CurrentWater;
	}

	public override void _ExitTree()
	{
		// Stats are automatically updated in GameData, no need to save explicitly

		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.ToolUsed -= OnToolUsed;
		}
	}
}
