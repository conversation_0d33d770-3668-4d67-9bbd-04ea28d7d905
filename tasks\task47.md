<my_description_before_refined_by_gemini_ai>

read Region10BossArea.tscn. Read Region10Boss.tscn. Read Region10BossProjectile.tscn. Read Region10SerpentFly.tscn.

I added: Region10BossArea, Region10Boss, Region10BossProjectile and Region10SerpentFly to world.tscn.
Now, I want you to design this boss fight system in following way:
* Our base scene is Region10BossArea.tscn. It will be placed in region 10
* In Region10BossArea we have following nodes:
** AnimationPlayer which has animation SpawnWalls. This animation will spawn walls around boss. Walls are hidden initially. When animation ends - walls are visible. This animation should play when player provides 5 [XX] - this means player clicks on button ButtonProvide and has 5 Turnips in inventory - then you need to subtract 5 Turnips from player inventory and play this animation.
** When player is in range and click Interact action (button E currently) when you should play animation from BoardToSpawnBoss->AnimationPlayerPanel 'Open' animation. It will make panel visible. When player closes panel (either by pressing ButtonExit or clicking Escape action) - then play 'Close' animation. Initially make BoardToSpawnBoss->CanvasLayer->Control->Node2D visible = false. Later, animations Open and Close will handle visibility and open/close this panel.
** Add translation to TEXT_SPAWN_BOSS_REGION_10 - inform player that will spawn boss and he need to give 'ofiarę' - 5 turnips to spawn boss, also tell it's dangerous and player should properly prepare.
** In BoardToSpawnBoss->Board->AnimationPlayerCandles you need to play Candles animation, and in AnimationPlayerBird - play BirdIdle animation. These animations need to be played initially. When player provides turnips, you need to play from AnimationPlayerBird animation 'BirdMove'
** Also, when player clicks this provide and he provides turnips, play PlayerController.ShakeCamera2S().
** When battle starts (player provides turnip), you also need to queue free BoardToSpawnBoss->PlayerDetector.
** We also have Region10Boss - this is boss object, you need to initially make it visible - false, and when player provides turnips - you need to make it visible - true.
** Also, when fight starts (player provides turnips), wait 3s then queue free Board.
** Implement boss logic to fight player in following way:
1) We have Region10Boss.tscn - read it. Make Sprite2D and AppearExplostion invisible initially. When boss should appear - play Appear animation. It will make visible animation, play animation and then make animation invisible but will make Sprite2D visible. When animation ends - play IdleDown animation. Also, in Region10BossArea we have BossPositions node - in which we have Node2D that should be used to indicate boss position. initially when appearing, should spawn at SpawnPosition. After 3s from appearing, he should move to Spot1 position (MoveUp animation)and then idle down. Boss has 125HP. Every 4s (after reaching spot1, spot2...spot5) boss should attack - which means spawn Region10BossProjectile at boss position and that projectiles should move just like an arrow to player position and when hit player (use player detector in player.tscn), then player has to get hit -2hp. Boss should spawn projectile that should move at player position (set speed adjustable and this 4s duration also adjustable). Player can attack boss like other enemies, with bow/sword. When boss gets hit - play Hit animation (HitDown/Up/Left/Right - depending on idle position so if idle down - play hit down etc). When boss has 80%hp or less, he should move to spot2 (MoveRight animation) and then idle down. Then when 60%hp or less - move to spot3 (MoveDown animation) and then idle down. Then when 40%hp or less - move to spot4 (MoveLeft animation) and then idle down. Then when 20%hp or less - move to spot5 (MoveUp animation) and then idle down. When boss reaches 0hp - play Die animation and then queue free boss and spawn CopperChest with 250 gold and 10 copper bars.
1.1) when boss is in spot1 and gets this <=80% hp - first spawn 1 Region10SerpentFly enemies then move boss to spot2. Same for other spots - spawn then go. In spot1 spawn 1, in spot2 spawn 2, in spot3 spawn 3, in spot4 spawn 4 and in spot5 spawn 5.
2) remember if boss is killed and also preserve state of this chest if spawned/not opened - like other chests. Use GameSaveData like in other places.
3) if boss is dead and you spawned chest - queue free Region10BossArea.
4) when restarting game - read if boss is already beated and then if yes, queue free Region10BossArea. 
5) Region10BossArea - all logic should execute only if region 10 is unlocked (if not unlodked and gets unlocked - this should initialize so dont skip this if not unlocked and not beated boss).
6) Implement Region10SerpentFly - read Region10SerpentFly.tscn. It's a simple enemy that should fly around and attack player. When player is in range - it should attack player. When player is not in range - it should fly to player (keep 16px distance from player when flying to it and then stop). Attack player after 1s of reaching this 16px distance, but then wait 3,5s before next attack. You can look at MeleeGoblin as example but melee goblin is complicated, here we will keep it simple, fly to player and attack. Serpent has 2HP and 1 attack. Serpent has 4 direction idle, hit (when he is hit), move and attack (when he attacks player - it has 18px attack range) (Up,Down,Left,Right) and also has Appear animation (play it when serpent is spawned then when it ends - idle down initially). Attack animations and die animaitions are not repeating - they play once, then play idle.

</my_description_before_refined_by_gemini_ai>

<my_description_after_refined_by_gemini_ai>
# Task: Implement Region 10 Boss Fight

## 1. `Region10BossArea.cs` Script Setup

- Create a new C# script named `Region10BossArea.cs` and attach it to the `Region10BossArea` node in `Region10BossArea.tscn`.
- This script will manage the entire boss fight sequence.

### 1.1. Node References

- Get references to the following nodes in `_Ready()`:
    - `AnimationPlayer`: `GetNode<AnimationPlayer>("AnimationPlayer")`
    - `BoardToSpawnBoss`: `GetNode<Node2D>("BoardToSpawnBoss")`
    - `PlayerDetector`: `BoardToSpawnBoss.GetNode<Area2D>("PlayerDetector")`
    - `AnimationPlayerPanel`: `BoardToSpawnBoss.GetNode<AnimationPlayer>("AnimationPlayerPanel")`
    - `AnimationPlayerCandles`: `BoardToSpawnBoss.GetNode<AnimationPlayer>("Board/AnimationPlayerCandles")`
    - `AnimationPlayerBird`: `BoardToSpawnBoss.GetNode<AnimationPlayer>("AnimationPlayerBird")`
    - `PanelControl`: `BoardToSpawnBoss.GetNode<Node2D>("CanvasLayer/Control/Node2D")`
    - `ButtonProvide`: `PanelControl.GetNode<Button>("ButtonProvide")`
    - `ButtonExit`: `PanelControl.GetNode<Button>("ButtonExit")`
    - `Region10Boss`: `GetNode<CharacterBody2D>("Region10Boss")`
    - `BossPositions`: `GetNode<Node2D>("BossPositions")`

### 1.2. Initial State

- In `_Ready()`:
    - Set `PanelControl.Visible = false;`
    - Set `Region10Boss.Visible = false;`
    - Play initial animations:
        - `AnimationPlayerCandles.Play("Candles");`
        - `AnimationPlayerBird.Play("BirdIdle");`
    - Connect to signals:
        - `PlayerDetector.BodyEntered += OnPlayerEnteredDetector;`
        - `PlayerDetector.BodyExited += OnPlayerExitedDetector;`
        - `ButtonProvide.Pressed += OnButtonProvidePressed;`
        - `ButtonExit.Pressed += OnButtonExitPressed;`
        - `CommonSignals.Instance.EKeyPressed += OnEKeyPressed;`

### 1.3. Player Interaction

- **`OnPlayerEnteredDetector(Node body)`**:
    - If `body` is the player, `CommonSignals.Instance.EmitShowKeyEPrompt();`
- **`OnPlayerExitedDetector(Node body)`**:
    - If `body` is the player, `CommonSignals.Instance.EmitHideKeyEPrompt();`
- **`OnEKeyPressed()`**:
    - If the player is inside the `PlayerDetector` area, play the "Open" animation from `AnimationPlayerPanel`.
- **`OnButtonProvidePressed()`**:
    - Check if `GameSaveData.Instance.PlayerResources.HasResource(ResourceType.Turnip, 5)`.
    - If true:
        - `GameSaveData.Instance.PlayerResources.RemoveResource(ResourceType.Turnip, 5);`
        - Start the boss fight sequence (see section 2).
- **`OnButtonExitPressed()`**:
    - Play the "Close" animation from `AnimationPlayerPanel`.

## 2. Boss Fight Sequence

- Create a new method `StartBossFight()`.
- **`StartBossFight()`**:
    - Play `AnimationPlayerPanel`'s "Close" animation.
    - `PlayerController.ShakeCamera2S();`
    - `AnimationPlayer.Play("SpawnWalls");`
    - `AnimationPlayerBird.Play("BirdMove");`
    - `PlayerDetector.QueueFree();`
    - `Region10Boss.Visible = true;`
    - `Region10Boss.GetNode<AnimationPlayer>("AnimationPlayer").Play("Appear");`
    - Create a timer for 3 seconds. On timeout, `BoardToSpawnBoss.GetNode<Sprite2D>("Board").QueueFree();`

## 3. `Region10Boss.cs` Script

- Create a new C# script `Region10Boss.cs` and attach it to the `Region10Boss` node.
- This script will handle the boss's behavior.

### 3.1. Boss Stats and State

- `HP = 125`
- `AttackDamage = 2`
- `AttackInterval = 4.0f`
- `MovementSpeed = 50.0f`
- An enum for the boss's current spot: `enum BossSpot { Spot1, Spot2, Spot3, Spot4, Spot5 }`
- A variable to track the current spot.

### 3.2. Boss Logic

- After the "Appear" animation finishes, play "IdleDown".
- After 3 seconds, start moving to `BossPositions/Spot1`.
- Once at a spot, start a timer for `AttackInterval`.
- **On attack timer timeout**:
    - Spawn a `Region10BossProjectile.tscn` instance.
    - The projectile should travel towards the player's position.
- **Taking Damage**:
    - Implement a `TakeDamage(int amount)` method.
    - When HP changes, update the `ProgressBar`.
    - When HP thresholds are crossed:
        - **<= 100 HP (80%)**: Spawn 1 `Region10SerpentFly`, then move to `Spot2`.
        - **<= 75 HP (60%)**: Spawn 2 `Region10SerpentFly`, then move to `Spot3`.
        - **<= 50 HP (40%)**: Spawn 3 `Region10SerpentFly`, then move to `Spot4`.
        - **<= 25 HP (20%)**: Spawn 4 `Region10SerpentFly`, then move to `Spot5`.
- **Death**:
    - When HP <= 0, play the "Die" animation.
    - On animation finished, `QueueFree()` the boss.
    - Spawn a `CopperChest` with 250 gold and 10 copper bars. Use the `ChestManager` logic as a reference for creating and saving the chest.

## 4. `Region10SerpentFly.cs` Script

- Create a new C# script `Region10SerpentFly.cs` and attach it to the `Region10SerpentFly` node.

### 4.1. Serpent Fly Behavior

- `HP = 2`
- `AttackDamage = 1`
- `AttackRange = 18.0f`
- `PlayerDistanceThreshold = 16.0f`
- `AttackCooldown = 3.5f`
- **Movement**:
    - If the player is outside `PlayerDistanceThreshold`, move towards the player.
    - If the player is within `PlayerDistanceThreshold`, stop.
- **Attack**:
    - After stopping for 1 second, play the attack animation.
    - After the attack, start the `AttackCooldown` timer.

## 5. Save/Load Logic

- In `GameData.cs`, add:
    - `public bool IsRegion10BossDefeated { get; set; } = false;`
- In `Region10BossArea.cs`:
    - In `_Ready()`, check `if (GameSaveData.Instance.IsRegion10BossDefeated) { QueueFree(); return; }`
    - Also check `if (!GameSaveData.Instance.UnlockedRegions.Contains(10)) { // Logic to handle waiting for unlock }`
- When the boss is defeated:
    - `GameSaveData.Instance.IsRegion10BossDefeated = true;`
    - `Region10BossArea.QueueFree();`
- Ensure the spawned chest is saved correctly in `GameSaveData.Instance.WorldData.Chests`.

## 6. Translations

- Add the following key to `data/translations.csv`:
    - `TEXT_SPAWN_BOSS_REGION_10,"This will spawn a dangerous boss. You must offer 5 turnips to proceed. Prepare for a tough fight!","To przywoła niebezpiecznego bossa. Musisz złożyć w ofierze 5 rzep, aby kontynuować. Przygotuj się na ciężką walkę!"...`
</my_description_after_refined_by_gemini_ai>