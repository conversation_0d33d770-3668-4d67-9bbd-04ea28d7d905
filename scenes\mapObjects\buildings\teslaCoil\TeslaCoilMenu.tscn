[gd_scene load_steps=25 format=3 uid="uid://b6gerw1frgtmt"]

[ext_resource type="Texture2D" uid="uid://b7sfyi050ihmh" path="res://resources/solaria/UI/upgrade_tesla_panel.png" id="1_07lk2"]
[ext_resource type="Script" uid="uid://dhakcfdx7f2dk" path="res://scenes/mapObjects/buildings/teslaCoil/TeslaCoilMenu.cs" id="1_teslacoilmenu_script"]
[ext_resource type="Texture2D" uid="uid://cdxye6tum1anb" path="res://resources/solaria/UI/inventory/close_button.png" id="2_xcrnw"]
[ext_resource type="Texture2D" uid="uid://bb0x815wcel3h" path="res://resources/solaria/buildings/tesla_coil.png" id="3_gop26"]
[ext_resource type="PackedScene" uid="uid://brynlg0mgkf76" path="res://scenes/UI/common/Label.tscn" id="4_tpmy4"]
[ext_resource type="Texture2D" uid="uid://dxnkb2dydn4p4" path="res://resources/solaria/resources/iron_bar_resource.png" id="5_icrgj"]
[ext_resource type="Texture2D" uid="uid://d0jb64dssfvc7" path="res://resources/solaria/resources/resource_beam.png" id="6_8ncgh"]
[ext_resource type="Texture2D" uid="uid://coohtgqediyob" path="res://resources/solaria/UI/dialogs/dialogGreenArrow.png" id="7_rcwqg"]
[ext_resource type="Texture2D" uid="uid://dvv01dmhem1hh" path="res://resources/solaria/UI/build/button2.png" id="8_acb3k"]

[sub_resource type="Animation" id="Animation_iw7eh"]
resource_name = "Close"
length = 0.09
step = 0.01
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Control/Panel:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.03, 0.09),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1.05, 1.05), Vector2(0.95, 0.95)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Control/Panel:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0.09),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}

[sub_resource type="Animation" id="Animation_r0oy0"]
resource_name = "Open"
length = 0.09
step = 0.01
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Control/Panel:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.06, 0.09),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(0.95, 0.95), Vector2(1.05, 1.05), Vector2(1, 1)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Control/Panel:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}

[sub_resource type="Animation" id="Animation_cna2i"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Control/Panel:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(0.95, 0.95)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Control/Panel:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_v0ypl"]
_data = {
&"Close": SubResource("Animation_iw7eh"),
&"Open": SubResource("Animation_r0oy0"),
&"RESET": SubResource("Animation_cna2i")
}

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_07hwm"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_oxiyu"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_mes8s"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_sapyx"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_b4v27"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_jlemb"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_3sn0u"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_kauif"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_jurd5"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_jpeu1"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_48vcc"]

[node name="TeslaCoilMenu" type="CanvasLayer"]
script = ExtResource("1_teslacoilmenu_script")

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_v0ypl")
}

[node name="Control" type="Control" parent="."]
layout_mode = 3
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -20.0
offset_top = -20.0
offset_right = 20.0
offset_bottom = 20.0
grow_horizontal = 2
grow_vertical = 2

[node name="Panel" type="Sprite2D" parent="Control"]
visible = false
scale = Vector2(0.95, 0.95)
texture = ExtResource("1_07lk2")

[node name="Close" type="Sprite2D" parent="Control/Panel"]
position = Vector2(91.7895, -90.7369)
texture = ExtResource("2_xcrnw")

[node name="TeslaSprite" type="Sprite2D" parent="Control/Panel"]
position = Vector2(-66.6316, -52.6312)
scale = Vector2(2, 2)
texture = ExtResource("3_gop26")

[node name="Header" parent="Control/Panel" instance=ExtResource("4_tpmy4")]
offset_left = -50.8422
offset_top = -69.4733
offset_right = 84.1578
offset_bottom = -46.4733
text = "TESLA_COIL_TEXT"

[node name="LevelText" parent="Control/Panel" instance=ExtResource("4_tpmy4")]
offset_left = -50.8422
offset_top = -52.0599
offset_right = 84.1578
offset_bottom = -29.0599
text = "Level 1"

[node name="UpgradeDescription" parent="Control/Panel" instance=ExtResource("4_tpmy4")]
offset_left = -85.0
offset_top = -11.0
offset_right = 135.0
offset_bottom = 12.0
scale = Vector2(0.775, 0.775)
text = "TESLA_COIL_UPGRADE_TEXT"
vertical_alignment = 0

[node name="Price1Text" parent="Control/Panel" instance=ExtResource("4_tpmy4")]
offset_left = -62.1053
offset_top = 10.5263
offset_right = -29.1053
offset_bottom = 31.5263
text = "12"

[node name="Price1" type="Sprite2D" parent="Control/Panel"]
position = Vector2(-30.5263, 20)
texture = ExtResource("5_icrgj")

[node name="Price2Text" parent="Control/Panel" instance=ExtResource("4_tpmy4")]
offset_left = -62.1053
offset_top = 27.3684
offset_right = -29.1053
offset_bottom = 48.3684
text = "12"

[node name="Price2" type="Sprite2D" parent="Control/Panel"]
position = Vector2(-31.5789, 36.8421)
texture = ExtResource("6_8ncgh")

[node name="TextDamage" parent="Control/Panel" instance=ExtResource("4_tpmy4")]
offset_left = -15.7895
offset_top = 9.47368
offset_right = 83.2105
offset_bottom = 30.4737
text = "Damage"

[node name="TextDamage2" parent="Control/Panel" instance=ExtResource("4_tpmy4")]
offset_left = -8.89474
offset_top = 29.1053
offset_right = 24.1053
offset_bottom = 50.1053
text = "1"
horizontal_alignment = 2

[node name="TextDamage3" parent="Control/Panel" instance=ExtResource("4_tpmy4")]
offset_left = 40.0
offset_top = 29.4737
offset_right = 70.0
offset_bottom = 50.4737
text = "2"
horizontal_alignment = 0

[node name="DamageArrow" type="Sprite2D" parent="Control/Panel"]
position = Vector2(31.4211, 39.3685)
rotation = -1.5708
texture = ExtResource("7_rcwqg")

[node name="Upgrade" type="Sprite2D" parent="Control/Panel"]
position = Vector2(0.368369, 66.1516)
scale = Vector2(0.845, 0.845)
texture = ExtResource("8_acb3k")

[node name="UpgradeText" parent="Control/Panel/Upgrade" instance=ExtResource("4_tpmy4")]
offset_left = -30.0
offset_top = -15.0
offset_right = 69.0
offset_bottom = 28.0
scale = Vector2(0.605, 0.605)
text = "UPGRADE_TEXT"

[node name="CloseButton" type="Button" parent="Control/Panel"]
offset_left = 82.1053
offset_top = -103.158
offset_right = 102.105
offset_bottom = -81.1579
theme_override_styles/focus = SubResource("StyleBoxEmpty_07hwm")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_oxiyu")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_mes8s")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_sapyx")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_b4v27")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_jlemb")
theme_override_styles/hover = SubResource("StyleBoxEmpty_3sn0u")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_kauif")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_jurd5")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_jpeu1")
theme_override_styles/normal = SubResource("StyleBoxEmpty_48vcc")

[node name="UpgradeButton" type="Button" parent="Control/Panel"]
offset_left = -27.3684
offset_top = 52.8358
offset_right = 27.6316
offset_bottom = 79.8358
theme_override_styles/focus = SubResource("StyleBoxEmpty_07hwm")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_oxiyu")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_mes8s")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_sapyx")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_b4v27")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_jlemb")
theme_override_styles/hover = SubResource("StyleBoxEmpty_3sn0u")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_kauif")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_jurd5")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_jpeu1")
theme_override_styles/normal = SubResource("StyleBoxEmpty_48vcc")
