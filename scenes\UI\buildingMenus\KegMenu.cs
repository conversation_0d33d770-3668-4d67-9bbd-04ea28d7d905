using Godot;
using System;

public partial class KegMenu : CanvasLayer, IMenu
{
	private AnimationPlayer _animationPlayer;
	private Control _control;
	private Sprite2D _panel;
	private Button _closeButton;
	private Button _beerButton;
	private Button _whiteWineButton;
	private Button _redWineButton;
	private Button _buttonMinusOne;
	private Button _buttonPlusOne;
	private Button _buttonSetOne;
	private Button _buttonSet25Percent;
	private Button _buttonSet50Percent;
	private Button _buttonSetMax;
	private Button _buttonProduce;
	private Label _amountToProduce;
	private Sprite2D _itemFront;

	private Keg _keg;
	private ResourceType _selectedResource = ResourceType.Beer;
	private int _currentAmount = 1;

	public override void _Ready()
	{
		_animationPlayer = GetNode<AnimationPlayer>("AnimationPlayer");
		_control = GetNode<Control>("Control");
		_panel = GetNode<Sprite2D>("Control/Panel");
		_closeButton = GetNode<Button>("Control/Panel/CloseButton");
		_beerButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListBeer/Button");
		_whiteWineButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListWhiteWine/Button");
		_redWineButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListRedWine/Button");
		_buttonMinusOne = GetNode<Button>("Control/Panel/InfoBoard/ButtonMinusOne");
		_buttonPlusOne = GetNode<Button>("Control/Panel/InfoBoard/ButtonPlusOne");
		_buttonSetOne = GetNode<Button>("Control/Panel/InfoBoard/ButtonSetOne");
		_buttonSet25Percent = GetNode<Button>("Control/Panel/InfoBoard/ButtonSet25Percent");
		_buttonSet50Percent = GetNode<Button>("Control/Panel/InfoBoard/ButtonSet50Percent");
		_buttonSetMax = GetNode<Button>("Control/Panel/InfoBoard/ButtonSetMax");
		_buttonProduce = GetNode<Button>("Control/Panel/InfoBoard/ButtonProduce");
		_amountToProduce = GetNode<Label>("Control/Panel/InfoBoard/AmountToProduce");
		_itemFront = GetNode<Sprite2D>("Control/Panel/InfoBoard/ItemFront");

		_closeButton.Pressed += OnCloseButtonPressed;
		_beerButton.Pressed += OnBeerButtonPressed;
		_whiteWineButton.Pressed += OnWhiteWineButtonPressed;
		_redWineButton.Pressed += OnRedWineButtonPressed;
		_buttonMinusOne.Pressed += OnMinusOnePressed;
		_buttonPlusOne.Pressed += OnPlusOnePressed;
		_buttonSetOne.Pressed += OnSetOnePressed;
		_buttonSet25Percent.Pressed += OnSet25PercentPressed;
		_buttonSet50Percent.Pressed += OnSet50PercentPressed;
		_buttonSetMax.Pressed += OnSetMaxPressed;
		_buttonProduce.Pressed += OnProducePressed;

		_panel.Visible = false;

		UpdateSelectedResource();

		// Register with MenuManager
		MenuManager.Instance?.RegisterMenu("KegMenu", this);
	}

	public override void _Input(InputEvent @event)
	{
		if (@event.IsActionPressed("Escape") && _panel.Visible)
		{
			CloseMenu();
		}
	}

	public void SetKeg(Keg keg)
	{
		_keg = keg;
	}

	public void OpenMenu(Keg keg)
	{
		_keg = keg;
		_selectedResource = ResourceType.Beer;
		_currentAmount = 1;
		UpdateSelectedResource();
		UpdateAmountDisplay();

		if (_animationPlayer != null && _animationPlayer.HasAnimation("Open"))
		{
			_animationPlayer.Play("Open");
		}
		else
		{
			_panel.Visible = true;
		}
	}

	public void CloseMenu()
	{
		if (_animationPlayer != null && _animationPlayer.HasAnimation("Close"))
		{
			_animationPlayer.Play("Close");
		}
		else
		{
			_panel.Visible = false;
		}
	}

	private void OnCloseButtonPressed()
	{
		if (MenuManager.Instance != null)
		{
			MenuManager.Instance.CloseMenu("KegMenu");
		}
		else
		{
			CloseMenu();
		}
	}

	private void OnBeerButtonPressed()
	{
		_selectedResource = ResourceType.Beer;
		UpdateSelectedResource();
	}

	private void OnWhiteWineButtonPressed()
	{
		_selectedResource = ResourceType.WhiteWine;
		UpdateSelectedResource();
	}

	private void OnRedWineButtonPressed()
	{
		_selectedResource = ResourceType.RedWine;
		UpdateSelectedResource();
	}

	private void OnMinusOnePressed()
	{
		_currentAmount = Math.Max(1, _currentAmount - 1);
		UpdateAmountDisplay();
	}

	private void OnPlusOnePressed()
	{
		_currentAmount++;
		UpdateAmountDisplay();
	}

	private void OnSetOnePressed()
	{
		_currentAmount = 1;
		UpdateAmountDisplay();
	}

	private void OnSet25PercentPressed()
	{
		int maxAmount = GetMaxCraftableAmount();
		_currentAmount = Math.Max(1, maxAmount / 4);
		UpdateAmountDisplay();
	}

	private void OnSet50PercentPressed()
	{
		int maxAmount = GetMaxCraftableAmount();
		_currentAmount = Math.Max(1, maxAmount / 2);
		UpdateAmountDisplay();
	}

	private void OnSetMaxPressed()
	{
		_currentAmount = GetMaxCraftableAmount();
		UpdateAmountDisplay();
	}

	private void OnProducePressed()
	{
		if (_keg != null && _keg.CanCraft(_selectedResource, _currentAmount))
		{
			_keg.StartCrafting(_selectedResource, _currentAmount);
			if (MenuManager.Instance != null)
			{
				MenuManager.Instance.CloseMenu("KegMenu");
			}
			else
			{
				CloseMenu();
			}
		}
	}

	private void UpdateSelectedResource()
	{
		if (_itemFront != null)
		{
			var texture = TextureManager.Instance?.GetResourceTexture(_selectedResource);
			_itemFront.Texture = texture;
		}
	}

	private void UpdateAmountDisplay()
	{
		if (_amountToProduce != null)
		{
			_amountToProduce.Text = _currentAmount.ToString();
		}
	}

	private int GetMaxCraftableAmount()
	{
		if (_keg == null) return 1;

		int maxAmount = 1;

		switch (_selectedResource)
		{
			case ResourceType.Beer:
				// Beer requires 3 wheat
				int wheatAvailable = GameSaveData.Instance.PlayerResources.GetResourceQuantity(ResourceType.Wheat);
				maxAmount = Math.Max(1, wheatAvailable / 3);
				break;
			case ResourceType.WhiteWine:
				// White wine requires 4 grapes
				int grapeAvailableWhite = GameSaveData.Instance.PlayerResources.GetResourceQuantity(ResourceType.Grape);
				maxAmount = Math.Max(1, grapeAvailableWhite / 4);
				break;
			case ResourceType.RedWine:
				// Red wine requires 5 grapes
				int grapeAvailableRed = GameSaveData.Instance.PlayerResources.GetResourceQuantity(ResourceType.Grape);
				maxAmount = Math.Max(1, grapeAvailableRed / 5);
				break;
		}

		return maxAmount;
	}

	// IMenu interface implementation
	public void OpenMenu()
	{
		if (_keg != null)
		{
			OpenMenu(_keg);
		}
	}

	public bool IsMenuOpen()
	{
		return _panel != null && _panel.Visible;
	}
}
