[gd_scene load_steps=7 format=3 uid="uid://dmnycy1kqxts2"]

[ext_resource type="Texture2D" uid="uid://cnr6xahe3v7go" path="res://resources/gamedevmarket/Super Pixel Projectiles Pack 2/spritesheet.png" id="1_yi6tc"]
[ext_resource type="Script" uid="uid://qvuqo6xyjds2" path="res://scripts/regions/region10/Region10BossProjectile.cs" id="2_projectile"]

[sub_resource type="Animation" id="Animation_xs0t6"]
resource_name = "Animate"
length = 3.0
loop_mode = 1
step = 0.1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite2D:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1, 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 1.7, 1.8, 1.9, 2, 2.1, 2.2, 2.3, 2.4, 2.5, 2.6, 2.7, 2.8, 2.9),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1),
"update": 1,
"values": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29]
}

[sub_resource type="Animation" id="Animation_sftqc"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite2D:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [0]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_2abab"]
_data = {
&"Animate": SubResource("Animation_xs0t6"),
&"RESET": SubResource("Animation_sftqc")
}

[sub_resource type="RectangleShape2D" id="RectangleShape2D_nk0o3"]
size = Vector2(16, 10)

[node name="Region10BossProjectile" type="CharacterBody2D"]
script = ExtResource("2_projectile")

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_2abab")
}

[node name="Sprite2D" type="Sprite2D" parent="."]
texture = ExtResource("1_yi6tc")
hframes = 30

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
position = Vector2(8, 0)
shape = SubResource("RectangleShape2D_nk0o3")

[node name="Hitbox" type="Area2D" parent="."]
collision_layer = 0
collision_mask = 4

[node name="CollisionShape2D" type="CollisionShape2D" parent="Hitbox"]
position = Vector2(8, 0)
shape = SubResource("RectangleShape2D_nk0o3")

[node name="LifetimeTimer" type="Timer" parent="."]
