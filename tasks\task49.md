We will be adding buildings this time. First, read BuildBuilding.md - this in short describes what it takes to add a building.

TASK-1:
Duplicate scene and logic (1:1, don't create from scratch, use the same but change names/icons etc) of SeedMaker.tscn and SeedMaker.cs and create CheesPress.tscn and CheesPress.cs. SeedMaker.tscn has SeedMakerMenu.tscn -> you need to duplicate SeedMakerMenu.tscn and SeedMakerMenu.cs similar way as and replace it in CheesPress.tscn.

In CheesPress, it has 16px width and 16px height just like seed maker. CheesPress image is here: res://resources/solaria/buildings/chees_press.png. There is also icon for build panel res://resources/solaria/buildings/chees_press_icon.png. Implement it so that i can build and use it just like seed maker. You have guidence in BuildBuilding.md.

In CheesPressMenu I want to have a single item initially in that item list -> Chees. To create chees you need 5 milk. Also, add <PERSON> and <PERSON>ees to texture manager, Milk and Chees resource type, ItemInformation and translations. Chees gives +20 food and +5 hp. Milk gives +10 water (for player when used).

To build chees press you need 15 planks and 10 iron bars.

I will set textures of chees and milk in texture manager and in chees press menu.

TASK-2:
Look at what you added in TASK-1 in order to add CheesPress. Similar to TASK-1, now add Keg. Keg image is here: res://resources/solaria/buildings/keg.png. There is also icon for build panel res://resources/solaria/buildings/keg_icon.png. Implement it so that i can build and use it just like seed maker. You have guidence in BuildBuilding.md. Add resource types like in TASK-1: Beer, WhiteWine, RedWine - and in menu of Keg. Make sure you don't create a menu/building from scratch but duplicate from seed maker - just like in TASK-1.

I will add textures of resources and their icons.

TASK-3:
Look at what you added in TASK-1 in order to add CheesPress. Similar to TASK-1, now add MayoMaker. MayoMaker image is here: res://resources/solaria/buildings/mayo_maker.png. There is also icon for build panel res://resources/solaria/buildings/mayo_maker_icon.png. Implement it so that i can build and use it just like seed maker. You have guidence in BuildBuilding.md. Add resource types like in TASK-1: Mayo - and in menu of MayoMaker. Make sure you don't create a menu/building from scratch but duplicate from seed maker - just like in TASK-1.

I will add textures of resources and their icons.

TASK-4:
Look at what you added in TASK-1 in order to add CheesPress. Similar to TASK-1, now add Smoker. Smoker image is here: res://resources/solaria/buildings/smoker.png. There is also icon for build panel res://resources/solaria/buildings/smoker_icon.png. Implement it so that i can build and use it just like seed maker. You have guidence in BuildBuilding.md. Add resource types like in TASK-1: SmokedFish - and in menu of Smoker. Make sure you don't create a menu/building from scratch but duplicate from seed maker - just like in TASK-1.

I will add textures of resources and their icons.

TASK-5:
Look at what you added in TASK-1 in order to add CheesPress. Similar to TASK-1, now add JamMaker. JamMaker image is here: res://resources/solaria/buildings/jam_maker.png. There is also icon for build panel res://resources/solaria/buildings/jam_maker_icon.png. Implement it so that i can build and use it just like seed maker. You have guidence in BuildBuilding.md. Add resource types like in TASK-1: BerryJam and StrawberryJam - and in menu of JamMaker. Make sure you don't create a menu/building from scratch but duplicate from seed maker - just like in TASK-1.

I will add textures of resources and their icons.

TASK-6:
Look at what you added in TASK-1 in order to add CheesPress. Similar to TASK-1, now add OilMaker. OilMaker image is here: res://resources/solaria/buildings/oil_maker.png. There is also icon for build panel res://resources/solaria/buildings/oil_maker_icon.png. Implement it so that i can build and use it just like seed maker. You have guidence in BuildBuilding.md. Add resource types like in TASK-1: SunflowerOil (from exising Sunflower resource type) - and in menu of OilMaker. Make sure you don't create a menu/building from scratch but duplicate from seed maker - just like in TASK-1.

I will add textures of resources and their icons.

TASK-7:
Create file task49_output.md and describe what you created, what i need to add (i mean what textures etc) and what is cost of creating each building, what it can produce from what resources etc.