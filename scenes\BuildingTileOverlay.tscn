[gd_scene load_steps=3 format=3 uid="uid://cn3e1n1asqgo8"]

[ext_resource type="Script" uid="uid://817oaq06yld7" path="res://scenes/BuildingTileOverlay.cs" id="1_overlay_script"]
[ext_resource type="Texture2D" uid="uid://cs5f2jsaqtuxe" path="res://resources/solaria/UI/placeholderSingleField.png" id="2_overlay_texture"]

[node name="BuildingTileOverlay" type="Node2D"]
scale = Vector2(0.7, 0.7)
script = ExtResource("1_overlay_script")

[node name="OverlaySprite" type="Sprite2D" parent="."]
modulate = Color(1, 1, 1, 0.8)
texture = ExtResource("2_overlay_texture")
