# Building Implementation Architecture

## System Overview

The building system provides a comprehensive framework for placeable structures with health management, interaction systems, crafting interfaces, and full save/load persistence. All buildings follow consistent patterns for placement, damage handling, and integration with the game's tile and region systems.

## Core Architecture

### Building Components
- **Placement System**: Preview validation, tile occupation, and final positioning
- **Health System**: Damage handling, health bars, hit animations, and destruction
- **Interaction System**: Player detection, menu triggering, and UI integration
- **Persistence System**: Save/load functionality with region-specific data management

### Required Scene Structure
All building scenes follow a standardized node hierarchy:
- **Root Node2D**: Main building container with positioning and script attachment
- **Sprite2D**: Visual representation with proper texture and positioning
- **ProgressBar**: Health visualization (initially hidden, shown on damage)
- **PlayerDetector (Area2D)**: Interaction zone with collision detection
- **AnimationPlayer**: Hit effects and visual feedback animations

## Building Categories

### Production Buildings
**Examples**: Furnaces, Anvils, Workbenches
**Pattern**: Resource processing with crafting interfaces
**Key Features**: Recipe systems, progress tracking, resource input/output management
**Reference Files**: `Furnace1.cs`, `Anvil.cs`, `AnvilMenu.cs`

### Utility Buildings
**Examples**: Bridges, Walls, Gates
**Pattern**: Environmental modification and player navigation
**Key Features**: Z-index management, collision modification, terrain interaction
**Reference Files**: `Bridge.cs`

### Interactive Buildings
**Examples**: Storage containers, Information boards
**Pattern**: Data storage and player interaction interfaces
**Key Features**: Inventory management, UI integration, state persistence
**Reference Files**: Various menu implementations in `scenes/ui/menus/`

## Implementation Requirements

### Core Interface Implementation
All buildings must implement `IDestroyableObject` interface providing:
- `TakeDamage(int damage)` method for health management
- Consistent damage handling and destruction behavior
- Integration with combat and interaction systems

### Enemy Combat Integration
Most buildings should also implement `ICombatTarget` interface for enemy targeting:
- `GetTargetType()` - Returns building priority (typically `TargetType.ProductionBuilding`)
- `CanBeTargeted()` - Returns true if building is placed, not destroyed, and has health > 0
- `GetTargetPosition()` - Returns `GlobalPosition` for enemy pathfinding
- `OnTargeted(Node2D enemy)` - Called when enemy starts targeting (usually empty)
- `OnAttacked(int damage, EnemyType attackerType)` - Delegates to existing `TakeDamage(damage)` method

**Exception**: Utility buildings like bridges typically do NOT implement ICombatTarget as they should not be targeted by enemies.

### Save Data Structure
Each building type requires a corresponding save data class with:
- Position and health state preservation
- Region assignment for proper loading
- Building-specific configuration data
- Integration with `ResourcesManager` serialization

### Tile System Integration
Buildings must properly interact with the tile system:
- Mark occupied tiles in `CustomDataLayerManager`
- Handle multi-tile building footprints correctly
- Clear tile occupation on destruction
- Validate placement against existing objects

## Integration Points

### Region Management
**Files**: `scenes/regions/RegionXManager.cs`
- Building lifecycle management per region
- Save/load coordination with ResourcesManager
- Spawn and cleanup handling during region transitions

### Build Menu System
**Files**: `scenes/ui/BuildMenu.cs`, `scenes/ui/BuildMenu.tscn`
- Building selection and preview interfaces
- Cost validation and resource checking
- Placement mode activation and validation

### Resource Management
**Files**: `scenes/ResourcesManager.cs`
- Centralized save/load functionality for all building types
- Region-specific data organization
- JSON serialization with GlobalJsonOptions integration

### Player Interaction
**Files**: `scenes/PlayerController.cs`
- Interaction detection and menu triggering
- Building placement validation and execution
- Combat integration for building damage
## Technical Patterns

### Health Management
- Health bars start hidden and appear on first damage
- Consistent hit animation using color modulation (ff6c44)
- Smooth health bar updates with percentage-based display
- Destruction handling with proper cleanup and tile clearing

### Menu Integration
- Dynamic menu instantiation through PlayerDetector interaction
- Building reference passing to menu systems for state management
- Consistent UI patterns across all building types

### Animation System
- Standardized hit animations with color modulation
- Consistent timing and visual feedback across all buildings
- Proper animation state management and restoration

## File Organization

### Building Implementations
- `scenes/mapObjects/buildings/` - All building scene files and scripts
- Individual building classes inherit common patterns and interfaces

### UI Systems
- `scenes/ui/menus/` - Building-specific menu implementations
- `scenes/ui/BuildMenu.cs` - Central building placement interface

### Integration Systems
- `scenes/regions/` - Region-specific building management
- `scenes/ResourcesManager.cs` - Centralized persistence
- `scenes/CustomDataLayerManager.cs` - Tile occupation tracking

### Supporting Infrastructure
- `scenes/CommonSignals.cs` - Event coordination between systems
- `scenes/GameSaveData.cs` - Data structure definitions
- `scenes/GlobalJsonOptions.cs` - Serialization configuration

## Implementation Reference

### Building Class Structure
Reference existing building implementations for proper structure and patterns:
- **Basic Production Building**: `scenes/mapObjects/buildings/Anvil.cs` - Complete example with IDestroyableObject and ICombatTarget
- **Crafting Building**: `scenes/mapObjects/buildings/Campfire.cs` - Recipe system and progress tracking
- **Multi-Resource Building**: `scenes/mapObjects/buildings/Furnace1.cs` - Multiple input/output resources
- **Tool Upgrade Building**: `scenes/mapObjects/buildings/Workbench.cs` - Complex upgrade system with progress tracking

### Target Type Guidelines
- **TargetType.ProductionBuilding**: Anvils, Furnaces, Workbenches, Grindstones, SeedMakers
- **TargetType.CombatBuilding**: Defensive structures that can fight back (future implementation)
- **TargetType.Wall**: Defensive barriers and walls (future implementation)
- **No ICombatTarget**: Bridges, decorative items, utility structures

## Design Philosophy

### Consistency First
All buildings follow identical patterns for core functionality while allowing customization for specific features. This ensures predictable behavior and maintainable code.

### Integration-Focused
Buildings are designed to work seamlessly with existing game systems rather than as isolated components. This includes proper integration with combat, regions, save/load, UI systems, and enemy AI.

### Extensible Architecture
The system is designed to easily accommodate new building types while maintaining backward compatibility and consistent behavior patterns.
