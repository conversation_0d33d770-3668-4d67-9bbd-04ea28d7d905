Implement following tasks. Make sure that you deeply understand how it all works so read all required files so that you correctly implement/fix issues.

TASK-1.1: 
Player has water and food. By default they have 30 max food and water and initially has 30 food and water.
Currently: Food and water are consumed when the player performs actions that involve using a tool.
Food: 1 unit of food is consumed for every 2 tool uses.
Water: 1 unit of water is consumed for every 3 tool uses. 
I want to change it so that food is consumed every 4 tool uses and water every 5 tool uses.

TASK-1.2: 
Currently:
Speed Reduction:
If either food or water is 0, the player's speed is reduced to 60% of normal.
If both food and water are 0, the player's speed is reduced to 40% of normal.
Starvation/Dehydration:
When food or water reaches 0, a 30-second timer starts.
If the timer completes and either food or water is still 0, the player dies.
Danger Overlay: A red overlay appears on the screen when either food or water reaches 0. The overlay becomes more intense if both are at 0.

I want to change it so that:
Starvation/Dehydration:
When food or water reaches, i don't want to add 30s timer and I don't want to kill player. Just reduce speed.
Danger Overlay: this does not work, i can't see it. Analyse in depth why it is not working and fix it.

TASK-2:
When i click b and build something then i click b - build menu is not opening. but when i click b again - it opens. i want it to open when i build something and click b again. when i select item to build, menu disapears but probably something is not updated and when i click b again it's not opening because it is trying to close this menu first and this is why i suspect. - fix it

TASK-3:
Melee Goblin - should have 5hp and 1 attack. Adjust.

TASK-4:
I completed all tutorial npc quests then region 4 was unlocked but not on MapPanel - on MapPanel region 4 was not unlocked and this results that I can't unlock region 5 - fix. Actually, after restart game region 4 is unlocked in MapPanel but shoul be also unlocked when i complete tutorial quests.

TASK-5:
Tutorial Npc, in one of dialogs says to go to fireplace and click R when i am close - this is incorrect, translation should says to click E, not R (in all languages) - fix

TASK-6:
Verify what is the condition to build a bridge. Something broke here. Bridge sohuld be buildable only on water that have CanBridge = true. Verify if it's working. Currently i tryed to build a bridge and i only could build it on the ground tile (probably wrong check was done there) and i couldnt build on tiles that has CanBridge=true. It's on LayerCanBridge layer in world tscn and somewhere in game data probably (i'm not sure where). Fix it